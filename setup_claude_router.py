#!/usr/bin/env python3
"""
Claude Code Router 配置脚本
将Claude Code请求路由到本地GLM-4.5
"""

import json
import os
import subprocess
import requests
import time
from pathlib import Path

class ClaudeRouterManager:
    def __init__(self):
        self.config_dir = Path("configs/claude_router")
        self.config_dir.mkdir(exist_ok=True)
        self.router_config_file = self.config_dir / "config.json"
        self.local_apis = {
            "vllm": "http://localhost:8000/v1",
            "lm_studio": "http://localhost:1234/v1"
        }
    
    def create_router_config(self):
        """创建claude-code-router配置"""
        config = {
            "port": 3000,
            "host": "localhost",
            "routes": [
                {
                    "path": "/v1/messages",
                    "method": "POST",
                    "target": {
                        "baseURL": self.local_apis["vllm"],
                        "endpoint": "/chat/completions",
                        "transform": "claude_to_openai"
                    },
                    "fallback": {
                        "baseURL": self.local_apis["lm_studio"],
                        "endpoint": "/chat/completions",
                        "transform": "claude_to_openai"
                    }
                }
            ],
            "transforms": {
                "claude_to_openai": {
                    "request": {
                        "model": "glm-4.5-air",
                        "messages": "$.messages",
                        "max_tokens": "$.max_tokens",
                        "temperature": "$.temperature",
                        "top_p": "$.top_p",
                        "stream": "$.stream"
                    },
                    "response": {
                        "id": "$.id",
                        "type": "message",
                        "role": "assistant",
                        "content": [
                            {
                                "type": "text",
                                "text": "$.choices[0].message.content"
                            }
                        ],
                        "model": "$.model",
                        "stop_reason": "end_turn",
                        "stop_sequence": null,
                        "usage": {
                            "input_tokens": "$.usage.prompt_tokens",
                            "output_tokens": "$.usage.completion_tokens"
                        }
                    }
                }
            },
            "middleware": [
                {
                    "name": "cors",
                    "options": {
                        "origin": "*",
                        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                        "headers": ["Content-Type", "Authorization", "anthropic-version"]
                    }
                },
                {
                    "name": "auth",
                    "options": {
                        "bypass": true
                    }
                },
                {
                    "name": "logging",
                    "options": {
                        "level": "info",
                        "file": "./logs/claude_router.log"
                    }
                }
            ]
        }
        
        with open(self.router_config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Claude Router配置已创建: {self.router_config_file}")
        return config
    
    def create_simple_proxy(self):
        """创建简单的代理服务器"""
        proxy_script = '''#!/usr/bin/env python3
"""
简单的Claude API代理服务器
将Claude Code请求转发到本地GLM-4.5
"""

from flask import Flask, request, jsonify, Response
import requests
import json
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# 本地API配置
LOCAL_APIS = {
    "vllm": "http://localhost:8000/v1",
    "lm_studio": "http://localhost:1234/v1"
}

def transform_claude_to_openai(claude_request):
    """将Claude API请求转换为OpenAI格式"""
    openai_request = {
        "model": "glm-4.5-air",
        "messages": claude_request.get("messages", []),
        "max_tokens": claude_request.get("max_tokens", 1000),
        "temperature": claude_request.get("temperature", 0.7),
        "top_p": claude_request.get("top_p", 0.9),
        "stream": claude_request.get("stream", False)
    }
    return openai_request

def transform_openai_to_claude(openai_response):
    """将OpenAI API响应转换为Claude格式"""
    if "choices" in openai_response and openai_response["choices"]:
        content = openai_response["choices"][0]["message"]["content"]
        claude_response = {
            "id": openai_response.get("id", "msg_001"),
            "type": "message",
            "role": "assistant",
            "content": [{"type": "text", "text": content}],
            "model": "claude-3-sonnet-20240229",
            "stop_reason": "end_turn",
            "stop_sequence": None,
            "usage": {
                "input_tokens": openai_response.get("usage", {}).get("prompt_tokens", 0),
                "output_tokens": openai_response.get("usage", {}).get("completion_tokens", 0)
            }
        }
        return claude_response
    return {"error": "Invalid response format"}

@app.route("/v1/messages", methods=["POST"])
def proxy_messages():
    """代理Claude messages API"""
    try:
        claude_request = request.get_json()
        openai_request = transform_claude_to_openai(claude_request)
        
        # 尝试vLLM API
        try:
            response = requests.post(
                f"{LOCAL_APIS['vllm']}/chat/completions",
                json=openai_request,
                timeout=30
            )
            if response.status_code == 200:
                openai_response = response.json()
                claude_response = transform_openai_to_claude(openai_response)
                return jsonify(claude_response)
        except Exception as e:
            app.logger.warning(f"vLLM API failed: {e}")
        
        # 回退到LM Studio API
        try:
            response = requests.post(
                f"{LOCAL_APIS['lm_studio']}/chat/completions",
                json=openai_request,
                timeout=30
            )
            if response.status_code == 200:
                openai_response = response.json()
                claude_response = transform_openai_to_claude(openai_response)
                return jsonify(claude_response)
        except Exception as e:
            app.logger.error(f"LM Studio API failed: {e}")
        
        return jsonify({"error": "All local APIs failed"}), 500
        
    except Exception as e:
        app.logger.error(f"Proxy error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route("/health", methods=["GET"])
def health_check():
    """健康检查"""
    status = {"status": "ok", "apis": {}}
    
    for name, url in LOCAL_APIS.items():
        try:
            response = requests.get(f"{url}/models", timeout=5)
            status["apis"][name] = "online" if response.status_code == 200 else "offline"
        except:
            status["apis"][name] = "offline"
    
    return jsonify(status)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=3000, debug=False)
'''
        
        proxy_file = Path("claude_proxy.py")
        with open(proxy_file, "w", encoding="utf-8") as f:
            f.write(proxy_script)
        
        print(f"✅ 简单代理服务器已创建: {proxy_file}")
        return proxy_file
    
    def install_router_dependencies(self):
        """安装代理服务器依赖"""
        try:
            subprocess.run([
                "pip", "install", "flask", "requests"
            ], check=True)
            print("✅ 代理服务器依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    
    def create_startup_scripts(self):
        """创建启动脚本"""
        # Windows批处理脚本
        bat_script = '''@echo off
echo 启动Claude Code Router代理服务...

REM 激活Python环境
call venv\\Scripts\\activate.bat

REM 启动代理服务器
echo 正在启动代理服务器 (端口3000)...
python claude_proxy.py

pause
'''
        
        with open("start_claude_router.bat", "w", encoding="utf-8") as f:
            f.write(bat_script)
        
        # PowerShell脚本
        ps_script = '''# 启动Claude Code Router代理服务
Write-Host "启动Claude Code Router代理服务..." -ForegroundColor Green

# 激活Python环境
& ".\\venv\\Scripts\\Activate.ps1"

# 检查本地API状态
Write-Host "检查本地API状态..." -ForegroundColor Yellow
$vllmStatus = try { Invoke-RestMethod -Uri "http://localhost:8000/v1/models" -TimeoutSec 5 } catch { $null }
$lmStudioStatus = try { Invoke-RestMethod -Uri "http://localhost:1234/v1/models" -TimeoutSec 5 } catch { $null }

if ($vllmStatus) {
    Write-Host "✅ vLLM API 可用" -ForegroundColor Green
} else {
    Write-Host "⚠️  vLLM API 不可用" -ForegroundColor Yellow
}

if ($lmStudioStatus) {
    Write-Host "✅ LM Studio API 可用" -ForegroundColor Green
} else {
    Write-Host "⚠️  LM Studio API 不可用" -ForegroundColor Yellow
}

if (-not $vllmStatus -and -not $lmStudioStatus) {
    Write-Host "❌ 没有可用的本地API，请先启动vLLM或LM Studio" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 启动代理服务器
Write-Host "正在启动代理服务器 (端口3000)..." -ForegroundColor Yellow
python claude_proxy.py
'''
        
        with open("start_claude_router.ps1", "w", encoding="utf-8") as f:
            f.write(ps_script)
        
        print("✅ 启动脚本已创建")
        print("  - start_claude_router.bat (Windows批处理)")
        print("  - start_claude_router.ps1 (PowerShell)")
    
    def test_proxy(self):
        """测试代理服务器"""
        proxy_url = "http://localhost:3000"
        
        # 健康检查
        try:
            response = requests.get(f"{proxy_url}/health", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print("✅ 代理服务器健康检查通过")
                print(f"API状态: {status['apis']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 代理服务器连接失败: {e}")
            return False
    
    def create_claude_code_config(self):
        """创建Claude Code配置"""
        claude_config = {
            "anthropic": {
                "base_url": "http://localhost:3000",
                "api_key": "dummy-key-for-local"
            },
            "model": "claude-3-sonnet-20240229"
        }
        
        config_file = Path("claude_code_config.json")
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(claude_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Claude Code配置已创建: {config_file}")
        
        # 创建环境变量设置脚本
        env_script = '''@echo off
REM 设置Claude Code使用本地代理
set ANTHROPIC_BASE_URL=http://localhost:3000
set ANTHROPIC_API_KEY=dummy-key-for-local

echo Claude Code环境变量已设置:
echo   ANTHROPIC_BASE_URL=%ANTHROPIC_BASE_URL%
echo   ANTHROPIC_API_KEY=%ANTHROPIC_API_KEY%
echo.
echo 现在可以启动Claude Code了
pause
'''
        
        with open("set_claude_env.bat", "w", encoding="utf-8") as f:
            f.write(env_script)
        
        print("✅ 环境变量设置脚本已创建: set_claude_env.bat")

def main():
    print("=== Claude Code Router 配置管理器 ===")
    
    manager = ClaudeRouterManager()
    
    # 安装依赖
    print("安装代理服务器依赖...")
    if not manager.install_router_dependencies():
        return
    
    # 创建配置文件
    manager.create_router_config()
    manager.create_simple_proxy()
    manager.create_startup_scripts()
    manager.create_claude_code_config()
    
    print("\n=== 配置完成 ===")
    print("使用步骤:")
    print("1. 启动本地LLM服务 (vLLM或LM Studio)")
    print("2. 运行 start_claude_router.bat 启动代理")
    print("3. 运行 set_claude_env.bat 设置环境变量")
    print("4. 启动Claude Code")
    print("\n代理服务器地址: http://localhost:3000")
    print("健康检查: http://localhost:3000/health")

if __name__ == "__main__":
    main()
