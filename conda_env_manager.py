#!/usr/bin/env python3
"""
Conda环境管理工具
专门处理GLM-4.5项目的Conda环境
"""

import os
import sys
import subprocess
import json
from pathlib import Path

class CondaEnvManager:
    def __init__(self):
        self.project_root = Path.cwd()
        self.env_path = self.project_root / "env"
        self.conda_meta_path = self.env_path / "conda-meta"
        
    def check_conda_available(self):
        """检查Conda是否可用"""
        try:
            result = subprocess.run(['conda', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Conda可用: {result.stdout.strip()}")
                return True
            else:
                print("❌ Conda不可用")
                return False
        except FileNotFoundError:
            print("❌ Conda未安装或不在PATH中")
            return False
    
    def check_env_exists(self):
        """检查Conda环境是否存在"""
        if self.env_path.exists() and (self.env_path / "python.exe").exists():
            print(f"✅ Conda环境存在: {self.env_path}")
            return True
        else:
            print(f"❌ Conda环境不存在: {self.env_path}")
            return False
    
    def get_env_info(self):
        """获取环境信息"""
        if not self.check_env_exists():
            return None
        
        info = {
            "path": str(self.env_path),
            "python_version": "Unknown",
            "packages": []
        }
        
        # 获取Python版本
        try:
            python_exe = self.env_path / "python.exe"
            result = subprocess.run([str(python_exe), "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                info["python_version"] = result.stdout.strip()
        except:
            pass
        
        # 读取conda-meta信息
        if self.conda_meta_path.exists():
            meta_files = list(self.conda_meta_path.glob("*.json"))
            for meta_file in meta_files:
                try:
                    with open(meta_file, 'r') as f:
                        meta_data = json.load(f)
                        package_name = meta_data.get("name", "unknown")
                        package_version = meta_data.get("version", "unknown")
                        info["packages"].append(f"{package_name}=={package_version}")
                except:
                    pass
        
        return info
    
    def create_env(self):
        """创建Conda环境"""
        if self.check_env_exists():
            print("环境已存在，跳过创建")
            return True
        
        if not self.check_conda_available():
            print("Conda不可用，无法创建环境")
            return False
        
        print(f"创建Conda环境: {self.env_path}")
        try:
            cmd = ["conda", "create", "-p", str(self.env_path), "python=3.10", "-y"]
            result = subprocess.run(cmd, check=True)
            print("✅ Conda环境创建成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 创建环境失败: {e}")
            return False
    
    def activate_env(self):
        """激活环境（返回激活命令）"""
        if not self.check_env_exists():
            print("环境不存在，无法激活")
            return None
        
        if os.name == 'nt':
            return f"call activate {self.env_path}"
        else:
            return f"conda activate {self.env_path}"
    
    def install_packages(self, packages):
        """在环境中安装包"""
        if not self.check_env_exists():
            print("环境不存在，无法安装包")
            return False
        
        pip_exe = self.env_path / "Scripts" / "pip.exe" if os.name == 'nt' else self.env_path / "bin" / "pip"
        
        if not pip_exe.exists():
            print("pip不存在，无法安装包")
            return False
        
        print(f"安装包: {', '.join(packages)}")
        try:
            cmd = [str(pip_exe), "install"] + packages
            result = subprocess.run(cmd, check=True)
            print("✅ 包安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 包安装失败: {e}")
            return False
    
    def remove_env(self):
        """删除环境"""
        if not self.check_env_exists():
            print("环境不存在，无需删除")
            return True
        
        import shutil
        try:
            shutil.rmtree(self.env_path)
            print("✅ 环境删除成功")
            return True
        except Exception as e:
            print(f"❌ 环境删除失败: {e}")
            return False
    
    def export_env(self, output_file="environment.yml"):
        """导出环境配置"""
        if not self.check_env_exists():
            print("环境不存在，无法导出")
            return False
        
        try:
            cmd = ["conda", "env", "export", "-p", str(self.env_path), "-f", output_file]
            result = subprocess.run(cmd, check=True)
            print(f"✅ 环境配置已导出到: {output_file}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 导出失败: {e}")
            return False
    
    def show_status(self):
        """显示环境状态"""
        print("\n" + "="*60)
        print("GLM-4.5 Conda环境状态")
        print("="*60)
        
        # Conda可用性
        conda_available = self.check_conda_available()
        
        # 环境存在性
        env_exists = self.check_env_exists()
        
        if env_exists:
            info = self.get_env_info()
            if info:
                print(f"📍 环境路径: {info['path']}")
                print(f"🐍 Python版本: {info['python_version']}")
                print(f"📦 已安装包数量: {len(info['packages'])}")
                
                # 检查关键包
                key_packages = ["torch", "transformers", "vllm", "flask"]
                print("\n🔍 关键包状态:")
                for pkg in key_packages:
                    found = any(pkg in p for p in info['packages'])
                    status = "✅" if found else "❌"
                    print(f"  {status} {pkg}")
        
        # 激活命令
        if env_exists:
            activate_cmd = self.activate_env()
            print(f"\n🚀 激活命令: {activate_cmd}")
        
        print("="*60)
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "="*50)
            print("GLM-4.5 Conda环境管理器")
            print("="*50)
            print("1. 查看环境状态")
            print("2. 创建环境")
            print("3. 安装GLM-4.5依赖包")
            print("4. 导出环境配置")
            print("5. 删除环境")
            print("6. 退出")
            
            choice = input("\n请选择操作 (1-6): ").strip()
            
            if choice == "1":
                self.show_status()
            elif choice == "2":
                self.create_env()
            elif choice == "3":
                self.install_glm45_packages()
            elif choice == "4":
                self.export_env()
            elif choice == "5":
                confirm = input("确认删除环境? (y/N): ").strip().lower()
                if confirm == 'y':
                    self.remove_env()
            elif choice == "6":
                break
            else:
                print("无效选择")
            
            input("\n按Enter继续...")
    
    def install_glm45_packages(self):
        """安装GLM-4.5所需的包"""
        packages = [
            "torch", "torchvision", "torchaudio",
            "transformers>=4.44.0", "accelerate", "vllm",
            "fastapi", "uvicorn", "requests", "flask",
            "psutil", "huggingface_hub", "sentencepiece",
            "protobuf", "bitsandbytes", "auto-gptq", "optimum"
        ]
        
        return self.install_packages(packages)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="GLM-4.5 Conda环境管理器")
    parser.add_argument("--status", action="store_true", help="显示环境状态")
    parser.add_argument("--create", action="store_true", help="创建环境")
    parser.add_argument("--install", action="store_true", help="安装GLM-4.5依赖")
    parser.add_argument("--export", action="store_true", help="导出环境配置")
    
    args = parser.parse_args()
    
    manager = CondaEnvManager()
    
    if args.status:
        manager.show_status()
    elif args.create:
        manager.create_env()
    elif args.install:
        manager.install_glm45_packages()
    elif args.export:
        manager.export_env()
    else:
        manager.interactive_menu()

if __name__ == "__main__":
    main()
