#!/usr/bin/env python3
"""
GLM-4.5 模型下载和量化脚本
针对 RTX 4060 8GB 显存优化
"""

import os
import sys
import subprocess
from pathlib import Path
import requests
from huggingface_hub import snapshot_download, login
import torch

def check_gpu_memory():
    """检查GPU显存"""
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"检测到GPU: {torch.cuda.get_device_name(0)}")
        print(f"显存大小: {gpu_memory:.1f} GB")
        return gpu_memory
    else:
        print("未检测到CUDA GPU")
        return 0

def download_glm45_models():
    """下载适合8GB显存的GLM-4.5模型"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # 推荐的模型配置（按优先级排序）
    model_configs = [
        {
            "name": "GLM-4.5-Air-GGUF",
            "repo_id": "bartowski/glm-4.5-air-GGUF",
            "files": ["glm-4.5-air-Q4_K_M.gguf"],  # 约4-5GB
            "description": "GLM-4.5-Air Q4量化版本，适合8GB显存",
            "priority": 1
        },
        {
            "name": "GLM-4.5-Air-AWQ",
            "repo_id": "hugging-quants/GLM-4.5-Air-AWQ",
            "files": None,  # 下载整个仓库
            "description": "GLM-4.5-Air AWQ量化版本，高质量4bit量化",
            "priority": 2
        },
        {
            "name": "GLM-4.5-Air-Original",
            "repo_id": "THUDM/glm-4.5-air",
            "files": None,
            "description": "GLM-4.5-Air 原始模型（需要本地量化）",
            "priority": 3
        }
    ]
    
    print("=== 开始下载GLM-4.5模型 ===")
    
    for config in model_configs:
        try:
            print(f"\n下载 {config['name']}...")
            print(f"描述: {config['description']}")
            
            model_path = models_dir / config['name']
            
            if config['files']:
                # 下载特定文件
                for file in config['files']:
                    print(f"下载文件: {file}")
                    snapshot_download(
                        repo_id=config['repo_id'],
                        allow_patterns=[file],
                        local_dir=model_path,
                        local_dir_use_symlinks=False
                    )
            else:
                # 下载整个仓库
                snapshot_download(
                    repo_id=config['repo_id'],
                    local_dir=model_path,
                    local_dir_use_symlinks=False
                )
            
            print(f"✅ {config['name']} 下载完成")
            
            # 如果是优先级1的模型下载成功，可以选择停止
            if config['priority'] == 1:
                print("推荐模型下载完成，可以开始使用")
                break
                
        except Exception as e:
            print(f"❌ {config['name']} 下载失败: {e}")
            continue

def download_for_lm_studio():
    """为LM Studio下载GGUF格式模型"""
    print("\n=== 为LM Studio准备GGUF模型 ===")
    
    lm_studio_models = [
        {
            "name": "GLM-4.5-Air-Q4_K_M",
            "url": "https://huggingface.co/bartowski/glm-4.5-air-GGUF/resolve/main/glm-4.5-air-Q4_K_M.gguf",
            "size": "~4.5GB"
        },
        {
            "name": "GLM-4.5-Air-Q5_K_M", 
            "url": "https://huggingface.co/bartowski/glm-4.5-air-GGUF/resolve/main/glm-4.5-air-Q5_K_M.gguf",
            "size": "~5.5GB"
        }
    ]
    
    lm_studio_dir = Path("models/LM_Studio")
    lm_studio_dir.mkdir(exist_ok=True)
    
    for model in lm_studio_models:
        model_file = lm_studio_dir / f"{model['name']}.gguf"
        if model_file.exists():
            print(f"✅ {model['name']} 已存在")
            continue
            
        print(f"下载 {model['name']} ({model['size']})...")
        try:
            # 使用huggingface_hub下载
            from huggingface_hub import hf_hub_download
            hf_hub_download(
                repo_id="bartowski/glm-4.5-air-GGUF",
                filename=f"{model['name']}.gguf",
                local_dir=lm_studio_dir,
                local_dir_use_symlinks=False
            )
            print(f"✅ {model['name']} 下载完成")
        except Exception as e:
            print(f"❌ {model['name']} 下载失败: {e}")

def create_model_configs():
    """创建模型配置文件"""
    configs_dir = Path("configs")
    configs_dir.mkdir(exist_ok=True)
    
    # vLLM配置
    vllm_config = {
        "model": "./models/GLM-4.5-Air-AWQ",
        "tensor_parallel_size": 1,
        "gpu_memory_utilization": 0.85,  # 使用85%显存
        "max_model_len": 8192,  # 适合8GB显存的上下文长度
        "quantization": "awq",
        "dtype": "half",
        "enforce_eager": True,  # 减少显存使用
        "disable_custom_all_reduce": True
    }
    
    with open(configs_dir / "vllm_config.json", "w", encoding="utf-8") as f:
        import json
        json.dump(vllm_config, f, indent=2, ensure_ascii=False)
    
    # LM Studio配置
    lm_studio_config = {
        "model_path": "./models/LM_Studio/GLM-4.5-Air-Q4_K_M.gguf",
        "context_length": 8192,
        "gpu_layers": 35,  # 根据显存调整
        "threads": 8,  # 使用CPU线程
        "batch_size": 512
    }
    
    with open(configs_dir / "lm_studio_config.json", "w", encoding="utf-8") as f:
        json.dump(lm_studio_config, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置文件创建完成")

def main():
    print("GLM-4.5 模型下载和配置工具")
    print("=" * 50)
    
    # 检查GPU
    gpu_memory = check_gpu_memory()
    if gpu_memory < 7:
        print("⚠️  警告: 显存不足8GB，建议使用更激进的量化")
    
    # 检查磁盘空间
    import shutil
    free_space = shutil.disk_usage(".").free / 1024**3
    print(f"可用磁盘空间: {free_space:.1f} GB")
    
    if free_space < 20:
        print("❌ 磁盘空间不足，需要至少20GB空间")
        return
    
    # 下载模型
    download_glm45_models()
    download_for_lm_studio()
    create_model_configs()
    
    print("\n=== 下载完成 ===")
    print("模型文件位置:")
    print("  - vLLM模型: ./models/GLM-4.5-Air-AWQ/")
    print("  - LM Studio模型: ./models/LM_Studio/")
    print("  - 配置文件: ./configs/")

if __name__ == "__main__":
    main()
