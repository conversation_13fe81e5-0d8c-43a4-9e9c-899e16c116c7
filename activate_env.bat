@echo off
chcp 65001 >nul
title 激活GLM-4.5虚拟环境

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  激活GLM-4.5虚拟环境                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查虚拟环境是否存在
if not exist "env\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在: env\
    echo.
    echo 请先运行以下命令创建虚拟环境:
    echo   setup_environment.ps1
    echo.
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境: env\
call env\Scripts\activate.bat

REM 显示环境信息
echo.
echo ✅ 虚拟环境已激活
echo 📍 环境路径: %VIRTUAL_ENV%
echo 🐍 Python版本:
python --version
echo.

REM 检查关键包
echo 📦 检查关键包安装状态:
python -c "import torch; print(f'  ✅ PyTorch: {torch.__version__}')" 2>nul || echo "  ❌ PyTorch 未安装"
python -c "import transformers; print(f'  ✅ Transformers: {transformers.__version__}')" 2>nul || echo "  ❌ Transformers 未安装"
python -c "import vllm; print(f'  ✅ vLLM: {vllm.__version__}')" 2>nul || echo "  ❌ vLLM 未安装"
python -c "import flask; print(f'  ✅ Flask: {flask.__version__}')" 2>nul || echo "  ❌ Flask 未安装"

echo.
echo 🚀 现在可以运行GLM-4.5相关脚本了
echo.
echo 常用命令:
echo   python deploy_glm45.py          - 一键部署
echo   python vllm_server.py           - 启动vLLM服务器
echo   python claude_proxy.py          - 启动Claude代理
echo   python test_deployment.py       - 测试部署
echo   python config_validator.py      - 验证配置
echo.
echo 💡 提示: 此窗口保持打开状态，虚拟环境将持续激活
echo.

REM 保持窗口打开
cmd /k
