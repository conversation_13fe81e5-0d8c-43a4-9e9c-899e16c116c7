@echo off
chcp 65001 >nul
title 激活GLM-4.5虚拟环境

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  激活GLM-4.5虚拟环境                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Conda环境是否存在
if not exist "env\python.exe" (
    echo ❌ Conda环境不存在: env\
    echo.
    echo 请先运行以下命令创建Conda环境:
    echo   00 conda创建环境.bat
    echo.
    pause
    exit /b 1
)

REM 激活Conda环境
echo 🔧 激活Conda环境: env\
call activate .\env

REM 显示环境信息
echo.
echo ✅ Conda环境已激活
echo 📍 环境路径: %CONDA_PREFIX%
echo 🐍 Python版本:
python --version
echo.

REM 检查关键包
echo 📦 检查关键包安装状态:
python -c "import torch; print(f'  ✅ PyTorch: {torch.__version__}')" 2>nul || echo "  ❌ PyTorch 未安装"
python -c "import transformers; print(f'  ✅ Transformers: {transformers.__version__}')" 2>nul || echo "  ❌ Transformers 未安装"
python -c "import vllm; print(f'  ✅ vLLM: {vllm.__version__}')" 2>nul || echo "  ❌ vLLM 未安装"
python -c "import flask; print(f'  ✅ Flask: {flask.__version__}')" 2>nul || echo "  ❌ Flask 未安装"

echo.
echo 🚀 现在可以运行GLM-4.5相关脚本了
echo.
echo 常用命令:
echo   python deploy_glm45.py          - 一键部署
echo   python vllm_server.py           - 启动vLLM服务器
echo   python claude_proxy.py          - 启动Claude代理
echo   python test_deployment.py       - 测试部署
echo   python config_validator.py      - 验证配置
echo.
echo 💡 提示: 此窗口保持打开状态，Conda环境将持续激活
echo.

REM 保持窗口打开
cmd /k
