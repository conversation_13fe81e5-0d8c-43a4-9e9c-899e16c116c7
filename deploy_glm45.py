#!/usr/bin/env python3
"""
GLM-4.5 + <PERSON> Code 一键部署脚本
"""

import os
import sys
import subprocess
import time
from pathlib import Path

class GLMDeployer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.steps = [
            ("环境检查", self.check_environment),
            ("安装依赖", self.install_dependencies),
            ("下载模型", self.download_models),
            ("系统优化", self.optimize_system),
            ("启动服务", self.start_services),
            ("测试验证", self.test_deployment)
        ]
    
    def check_environment(self):
        """检查环境"""
        print("检查Python环境...")
        python_version = sys.version_info
        if python_version.major != 3 or python_version.minor < 8:
            raise Exception("需要Python 3.8或更高版本")
        print(f"✅ Python {python_version.major}.{python_version.minor}")
        
        print("检查CUDA...")
        try:
            result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ CUDA已安装")
            else:
                print("⚠️  CUDA未检测到，将使用CPU模式")
        except:
            print("⚠️  CUDA未检测到，将使用CPU模式")
        
        print("检查Node.js...")
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装，请先安装Node.js")
                return False
        except:
            print("❌ Node.js未安装，请先安装Node.js")
            return False
        
        return True
    
    def install_dependencies(self):
        """安装依赖"""
        print("检查Conda环境...")
        if not (self.project_root / "env" / "python.exe").exists():
            print("❌ Conda环境不存在")
            print("请先运行: 00 conda创建环境.bat")
            return False
        else:
            print("✅ Conda环境已存在: env/")

        # 使用Conda环境的Python和pip
        if os.name == 'nt':
            pip_path = self.project_root / "env" / "Scripts" / "pip.exe"
            python_path = self.project_root / "env" / "python.exe"
        else:
            pip_path = self.project_root / "env" / "bin" / "pip"
            python_path = self.project_root / "env" / "bin" / "python"
        
        print("安装Python依赖...")
        packages = [
            "torch", "torchvision", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu121",
            "transformers>=4.44.0", "accelerate", "vllm", "fastapi", "uvicorn",
            "requests", "flask", "psutil", "gpustat", "huggingface_hub"
        ]
        
        subprocess.run([str(pip_path), "install"] + packages, check=True)
        
        print("安装claude-code-router...")
        subprocess.run(["npm", "install", "-g", "@musistudio/claude-code-router"], check=True)
        
        return True
    
    def download_models(self):
        """下载模型"""
        print("下载GLM-4.5模型...")
        subprocess.run([sys.executable, "download_models.py"], check=True)
        return True
    
    def optimize_system(self):
        """系统优化"""
        print("执行系统优化...")
        subprocess.run([sys.executable, "system_optimizer.py"], check=True)
        return True
    
    def start_services(self):
        """启动服务"""
        print("配置服务...")
        
        # 配置vLLM
        subprocess.run([sys.executable, "vllm_server.py", "--action", "config"], check=True)
        
        # 配置LM Studio
        subprocess.run([sys.executable, "setup_lm_studio.py"], check=True)
        
        # 配置Claude Router
        subprocess.run([sys.executable, "setup_claude_router.py"], check=True)
        
        print("✅ 服务配置完成")
        print("\n手动启动步骤:")
        print("1. 启动vLLM: python vllm_server.py")
        print("2. 或启动LM Studio: start_lm_studio.bat")
        print("3. 启动Claude Router: start_claude_router.bat")
        print("4. 设置环境变量: set_claude_env.bat")
        
        return True
    
    def test_deployment(self):
        """测试部署"""
        print("运行部署测试...")
        subprocess.run([sys.executable, "test_deployment.py"], check=True)
        return True
    
    def run_deployment(self):
        """执行完整部署"""
        print("🚀 开始GLM-4.5 + Claude Code部署")
        print("=" * 60)
        
        for i, (step_name, step_func) in enumerate(self.steps, 1):
            print(f"\n[{i}/{len(self.steps)}] {step_name}")
            print("-" * 40)
            
            try:
                if step_func():
                    print(f"✅ {step_name} 完成")
                else:
                    print(f"❌ {step_name} 失败")
                    return False
            except Exception as e:
                print(f"❌ {step_name} 异常: {e}")
                return False
        
        print("\n🎉 部署完成!")
        self.print_usage_guide()
        return True
    
    def print_usage_guide(self):
        """打印使用指南"""
        guide = """
=== 使用指南 ===

1. 启动本地LLM服务:
   方案A (推荐): python vllm_server.py
   方案B (稳定): start_lm_studio.bat

2. 启动Claude代理:
   start_claude_router.bat

3. 设置Claude Code环境:
   set_claude_env.bat

4. 启动Claude Code并开始使用

=== 监控和调试 ===

- 性能监控: python performance_monitor.py
- 健康检查: http://localhost:3000/health
- API测试: python test_deployment.py

=== 配置文件 ===

- vLLM配置: configs/vllm_config.json
- LM Studio配置: configs/lm_studio_config.json
- Claude Router配置: configs/claude_router/config.json

=== 故障排除 ===

1. 显存不足: 减少max_model_len或使用更激进的量化
2. 推理慢: 增加GPU layers或减少并发数
3. 连接失败: 检查防火墙和端口占用

详细文档: OPTIMIZATION_GUIDE.md
"""
        print(guide)

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--quick-test":
        # 快速测试模式
        print("快速测试模式")
        subprocess.run([sys.executable, "test_deployment.py"])
        return
    
    deployer = GLMDeployer()
    
    print("GLM-4.5 + Claude Code 部署工具")
    print("适用于: RTX 4060 8GB + 64GB RAM + i7-13700H")
    print("=" * 60)
    
    choice = input("\n选择操作:\n1. 完整部署\n2. 仅测试现有部署\n3. 退出\n请输入选择 (1-3): ")
    
    if choice == "1":
        deployer.run_deployment()
    elif choice == "2":
        subprocess.run([sys.executable, "test_deployment.py"])
    elif choice == "3":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
