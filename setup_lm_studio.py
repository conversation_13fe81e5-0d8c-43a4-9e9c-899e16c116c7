#!/usr/bin/env python3
"""
LM Studio 配置和集成脚本
"""

import json
import os
import subprocess
import time
import requests
from pathlib import Path

class LMStudioManager:
    def __init__(self):
        self.api_base = "http://localhost:1234/v1"
        self.models_dir = Path("models/LM_Studio")
        self.config_file = Path("configs/lm_studio_config.json")
        
    def check_lm_studio_running(self):
        """检查LM Studio是否运行"""
        try:
            response = requests.get(f"{self.api_base}/models", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_optimal_settings(self):
        """获取RTX 4060 8GB的最优设置"""
        return {
            "context_length": 8192,  # 适中的上下文长度
            "gpu_layers": 35,        # 大部分层放GPU，少量放CPU
            "threads": 8,            # 利用i7-13700H的多线程
            "batch_size": 512,       # 平衡内存和性能
            "rope_freq_base": 10000,
            "rope_freq_scale": 1.0,
            "temperature": 0.7,
            "top_p": 0.9,
            "top_k": 40,
            "repeat_penalty": 1.1,
            "memory_lock": True,     # 锁定内存提高性能
            "use_mmap": True,        # 使用内存映射
            "use_mlock": True        # 锁定模型在内存中
        }
    
    def create_lm_studio_preset(self):
        """创建LM Studio预设配置"""
        preset = {
            "name": "GLM-4.5-RTX4060-Optimized",
            "description": "针对RTX 4060 8GB优化的GLM-4.5配置",
            "settings": self.get_optimal_settings()
        }
        
        presets_dir = Path("configs/lm_studio_presets")
        presets_dir.mkdir(exist_ok=True)
        
        with open(presets_dir / "glm45_rtx4060.json", "w", encoding="utf-8") as f:
            json.dump(preset, f, indent=2, ensure_ascii=False)
        
        print("✅ LM Studio预设配置已创建")
        return preset
    
    def wait_for_model_load(self, timeout=300):
        """等待模型加载完成"""
        print("等待模型加载...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.api_base}/models")
                if response.status_code == 200:
                    models = response.json()
                    if models.get("data") and len(models["data"]) > 0:
                        model_name = models["data"][0]["id"]
                        print(f"✅ 模型已加载: {model_name}")
                        return model_name
            except:
                pass
            
            time.sleep(5)
            print(".", end="", flush=True)
        
        print("\n❌ 模型加载超时")
        return None
    
    def test_api_connection(self):
        """测试API连接"""
        if not self.check_lm_studio_running():
            print("❌ LM Studio未运行或API服务器未启动")
            print("请启动LM Studio并开启本地服务器")
            return False
        
        model_name = self.wait_for_model_load()
        if not model_name:
            return False
        
        # 测试聊天API
        try:
            test_payload = {
                "model": model_name,
                "messages": [
                    {"role": "user", "content": "你好，请简单介绍一下你自己。"}
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = requests.post(
                f"{self.api_base}/chat/completions",
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"✅ API测试成功")
                print(f"模型响应: {content[:100]}...")
                return True
            else:
                print(f"❌ API测试失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False
    
    def create_startup_script(self):
        """创建LM Studio启动脚本"""
        script_content = '''@echo off
echo 启动LM Studio GLM-4.5服务...

REM 检查LM Studio是否已安装
where lms >nul 2>nul
if %errorlevel% neq 0 (
    echo LM Studio命令行工具未找到
    echo 请确保LM Studio已安装并添加到PATH
    pause
    exit /b 1
)

REM 启动LM Studio服务器
echo 正在启动LM Studio本地服务器...
start "" "C:\\Users\\<USER>\\AppData\\Local\\Programs\\LM Studio\\LM Studio.exe"

REM 等待服务器启动
timeout /t 10 /nobreak

echo LM Studio已启动
echo API地址: http://localhost:1234
echo 请在LM Studio中加载GLM-4.5模型并启用本地服务器
pause
'''
        
        with open("start_lm_studio.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("✅ LM Studio启动脚本已创建: start_lm_studio.bat")
    
    def generate_usage_guide(self):
        """生成使用指南"""
        guide = """
# LM Studio + GLM-4.5 使用指南

## 1. 启动LM Studio
```bash
# 方法1: 使用启动脚本
start_lm_studio.bat

# 方法2: 手动启动
# 打开LM Studio应用程序
```

## 2. 加载模型
1. 在LM Studio中点击"Load Model"
2. 选择: models/LM_Studio/GLM-4.5-Air-Q4_K_M.gguf
3. 使用推荐设置:
   - Context Length: 8192
   - GPU Layers: 35
   - CPU Threads: 8

## 3. 启用本地服务器
1. 在LM Studio中点击"Local Server"标签
2. 点击"Start Server"
3. 确认服务器运行在 http://localhost:1234

## 4. 测试连接
```python
python setup_lm_studio.py
```

## 5. 性能优化建议
- 如果显存不足，减少GPU Layers到25-30
- 如果推理速度慢，增加CPU Threads到12-16
- 如果内存不足，减少Context Length到4096

## 6. API使用示例
```python
import requests

response = requests.post("http://localhost:1234/v1/chat/completions", json={
    "model": "glm-4.5-air",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
})
print(response.json())
```
"""
        
        with open("LM_Studio_Guide.md", "w", encoding="utf-8") as f:
            f.write(guide)
        
        print("✅ 使用指南已创建: LM_Studio_Guide.md")

def main():
    print("=== LM Studio 配置管理器 ===")
    
    manager = LMStudioManager()
    
    # 创建配置文件
    manager.create_lm_studio_preset()
    manager.create_startup_script()
    manager.generate_usage_guide()
    
    # 检查LM Studio状态
    print("\n检查LM Studio状态...")
    if manager.check_lm_studio_running():
        print("✅ LM Studio正在运行")
        manager.test_api_connection()
    else:
        print("⚠️  LM Studio未运行")
        print("请运行 start_lm_studio.bat 启动LM Studio")
    
    print("\n=== 配置完成 ===")
    print("下一步:")
    print("1. 运行 start_lm_studio.bat 启动LM Studio")
    print("2. 在LM Studio中加载GLM-4.5模型")
    print("3. 启用本地服务器")
    print("4. 运行此脚本测试连接")

if __name__ == "__main__":
    main()
