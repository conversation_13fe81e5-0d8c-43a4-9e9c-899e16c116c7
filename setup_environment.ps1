# GLM-4.5 + Claude Code 环境设置脚本
# 适用于 Windows 11 + RTX 4060 8GB + 64GB RAM

Write-Host "=== GLM-4.5 + Claude Code 环境设置 ===" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "请以管理员身份运行此脚本" -ForegroundColor Red
    exit 1
}

# 1. 检查并安装 Python 3.11
Write-Host "检查 Python 环境..." -ForegroundColor Yellow
$pythonVersion = python --version 2>$null
if ($pythonVersion -match "Python 3\.1[1-9]") {
    Write-Host "Python 已安装: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "正在下载并安装 Python 3.11..." -ForegroundColor Yellow
    $pythonUrl = "https://www.python.org/ftp/python/3.11.9/python-3.11.9-amd64.exe"
    $pythonInstaller = "$env:TEMP\python-3.11.9-amd64.exe"
    Invoke-WebRequest -Uri $pythonUrl -OutFile $pythonInstaller
    Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1" -Wait
    Remove-Item $pythonInstaller
}

# 2. 检查 CUDA 工具包
Write-Host "检查 CUDA 环境..." -ForegroundColor Yellow
$cudaVersion = nvcc --version 2>$null
if ($cudaVersion) {
    Write-Host "CUDA 已安装: $($cudaVersion | Select-String 'release')" -ForegroundColor Green
} else {
    Write-Host "请手动安装 CUDA Toolkit 12.1 或更高版本" -ForegroundColor Red
    Write-Host "下载地址: https://developer.nvidia.com/cuda-downloads" -ForegroundColor Yellow
}

# 3. 检查 Git
Write-Host "检查 Git..." -ForegroundColor Yellow
$gitVersion = git --version 2>$null
if ($gitVersion) {
    Write-Host "Git 已安装: $gitVersion" -ForegroundColor Green
} else {
    Write-Host "正在安装 Git..." -ForegroundColor Yellow
    winget install --id Git.Git -e --source winget
}

# 4. 检查 Node.js (用于 claude-code-router)
Write-Host "检查 Node.js..." -ForegroundColor Yellow
$nodeVersion = node --version 2>$null
if ($nodeVersion) {
    Write-Host "Node.js 已安装: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "正在安装 Node.js..." -ForegroundColor Yellow
    winget install --id OpenJS.NodeJS -e --source winget
}

# 5. 创建项目目录结构
Write-Host "创建项目目录..." -ForegroundColor Yellow
$projectRoot = "D:\wwwroot\GLM-4.5"
$directories = @(
    "$projectRoot\models",
    "$projectRoot\cache",
    "$projectRoot\logs",
    "$projectRoot\configs",
    "$projectRoot\scripts"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force
        Write-Host "创建目录: $dir" -ForegroundColor Green
    }
}

# 6. 检查 Conda 环境
Write-Host "检查 Conda 环境..." -ForegroundColor Yellow
Set-Location $projectRoot
if (Test-Path "env\python.exe") {
    Write-Host "Conda环境已存在: env\" -ForegroundColor Green
    Write-Host "请手动激活环境: call activate .\env" -ForegroundColor Cyan
} else {
    Write-Host "Conda环境不存在" -ForegroundColor Yellow
    Write-Host "请运行: 00 conda创建环境.bat" -ForegroundColor Cyan
}

# 7. 升级 pip 和安装基础包
Write-Host "升级 pip 和安装基础包..." -ForegroundColor Yellow
python -m pip install --upgrade pip
pip install wheel setuptools

Write-Host "=== 环境设置完成 ===" -ForegroundColor Green
Write-Host "Conda环境位置: .\env\" -ForegroundColor Cyan
Write-Host "激活环境: call activate .\env" -ForegroundColor Cyan
Write-Host "下一步: 运行 install_dependencies.ps1 安装依赖包" -ForegroundColor Yellow
