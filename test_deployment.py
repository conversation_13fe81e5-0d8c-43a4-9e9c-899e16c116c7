#!/usr/bin/env python3
"""
GLM-4.5 + <PERSON> Code 部署测试脚本
"""

import requests
import time
import json
import subprocess
import threading
from pathlib import Path
import statistics

class DeploymentTester:
    def __init__(self):
        self.apis = {
            "vllm": "http://localhost:8000/v1",
            "lm_studio": "http://localhost:1234/v1",
            "claude_proxy": "http://localhost:3000"
        }
        self.test_results = {}
    
    def check_api_availability(self):
        """检查所有API的可用性"""
        print("=== 检查API可用性 ===")
        results = {}
        
        for name, url in self.apis.items():
            try:
                if name == "claude_proxy":
                    response = requests.get(f"{url}/health", timeout=5)
                else:
                    response = requests.get(f"{url}/models", timeout=5)
                
                if response.status_code == 200:
                    results[name] = "✅ 在线"
                    print(f"{name:15}: ✅ 在线")
                else:
                    results[name] = f"❌ 错误 ({response.status_code})"
                    print(f"{name:15}: ❌ 错误 ({response.status_code})")
            except Exception as e:
                results[name] = f"❌ 离线 ({str(e)[:30]})"
                print(f"{name:15}: ❌ 离线")
        
        return results
    
    def test_model_inference(self, api_name, api_url):
        """测试模型推理性能"""
        print(f"\n=== 测试 {api_name} 推理性能 ===")
        
        test_prompts = [
            "你好，请简单介绍一下你自己。",
            "请解释一下什么是机器学习。",
            "写一个Python函数来计算斐波那契数列。",
            "请分析一下当前AI技术的发展趋势。"
        ]
        
        results = {
            "response_times": [],
            "token_speeds": [],
            "success_count": 0,
            "total_tests": len(test_prompts)
        }
        
        for i, prompt in enumerate(test_prompts):
            print(f"测试 {i+1}/{len(test_prompts)}: {prompt[:30]}...")
            
            try:
                start_time = time.time()
                
                payload = {
                    "model": "glm-4.5-air",
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 200,
                    "temperature": 0.7
                }
                
                response = requests.post(
                    f"{api_url}/chat/completions",
                    json=payload,
                    timeout=30
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    # 估算token速度
                    token_count = len(content.split())
                    token_speed = token_count / response_time if response_time > 0 else 0
                    
                    results["response_times"].append(response_time)
                    results["token_speeds"].append(token_speed)
                    results["success_count"] += 1
                    
                    print(f"  ✅ 成功 ({response_time:.2f}s, ~{token_speed:.1f} tokens/s)")
                    print(f"  响应: {content[:100]}...")
                else:
                    print(f"  ❌ 失败 ({response.status_code})")
                    
            except Exception as e:
                print(f"  ❌ 异常: {str(e)[:50]}")
        
        # 计算统计信息
        if results["response_times"]:
            results["avg_response_time"] = statistics.mean(results["response_times"])
            results["avg_token_speed"] = statistics.mean(results["token_speeds"])
            results["success_rate"] = results["success_count"] / results["total_tests"]
        
        return results
    
    def test_claude_proxy(self):
        """测试Claude代理功能"""
        print("\n=== 测试Claude代理功能 ===")
        
        # 测试健康检查
        try:
            response = requests.get(f"{self.apis['claude_proxy']}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print("✅ 健康检查通过")
                print(f"  API状态: {health_data.get('apis', {})}")
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
        
        # 测试Claude格式API
        try:
            claude_payload = {
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 100,
                "messages": [
                    {"role": "user", "content": "Hello! Can you help me with coding?"}
                ]
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.apis['claude_proxy']}/v1/messages",
                json=claude_payload,
                timeout=30
            )
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("content", [{}])[0].get("text", "")
                print(f"✅ Claude API测试成功 ({end_time - start_time:.2f}s)")
                print(f"  响应: {content[:100]}...")
                return True
            else:
                print(f"❌ Claude API测试失败: {response.status_code}")
                print(f"  响应: {response.text[:200]}")
                return False
                
        except Exception as e:
            print(f"❌ Claude API测试异常: {e}")
            return False
    
    def benchmark_performance(self):
        """性能基准测试"""
        print("\n=== 性能基准测试 ===")
        
        # 并发测试
        def concurrent_request(api_url, results_list):
            try:
                payload = {
                    "model": "glm-4.5-air",
                    "messages": [{"role": "user", "content": "Count from 1 to 10."}],
                    "max_tokens": 50
                }
                
                start_time = time.time()
                response = requests.post(f"{api_url}/chat/completions", json=payload, timeout=20)
                end_time = time.time()
                
                if response.status_code == 200:
                    results_list.append(end_time - start_time)
            except:
                pass
        
        # 测试不同并发级别
        for concurrency in [1, 2, 4]:
            print(f"\n并发级别: {concurrency}")
            
            for api_name, api_url in [("vLLM", self.apis["vllm"]), ("LM Studio", self.apis["lm_studio"])]:
                if api_name.lower().replace(" ", "_") not in self.check_api_availability():
                    continue
                
                results = []
                threads = []
                
                start_time = time.time()
                for _ in range(concurrency):
                    thread = threading.Thread(target=concurrent_request, args=(api_url, results))
                    threads.append(thread)
                    thread.start()
                
                for thread in threads:
                    thread.join()
                
                total_time = time.time() - start_time
                
                if results:
                    avg_time = statistics.mean(results)
                    throughput = len(results) / total_time
                    print(f"  {api_name:12}: {len(results)}/{concurrency} 成功, "
                          f"平均 {avg_time:.2f}s, 吞吐量 {throughput:.2f} req/s")
                else:
                    print(f"  {api_name:12}: 所有请求失败")
    
    def create_test_report(self):
        """生成测试报告"""
        report = f"""# GLM-4.5 + Claude Code 部署测试报告

## 测试时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## API可用性测试
"""
        
        api_status = self.check_api_availability()
        for api, status in api_status.items():
            report += f"- **{api}**: {status}\n"
        
        report += "\n## 推理性能测试\n"
        
        # 测试各个API
        for api_name, api_url in self.apis.items():
            if api_name == "claude_proxy":
                continue
                
            if api_name in api_status and "在线" in api_status[api_name]:
                print(f"\n测试 {api_name} 性能...")
                results = self.test_model_inference(api_name, api_url)
                
                if results.get("avg_response_time"):
                    report += f"\n### {api_name}\n"
                    report += f"- 成功率: {results['success_rate']:.1%}\n"
                    report += f"- 平均响应时间: {results['avg_response_time']:.2f}秒\n"
                    report += f"- 平均token速度: {results['avg_token_speed']:.1f} tokens/秒\n"
        
        # Claude代理测试
        claude_success = self.test_claude_proxy()
        report += f"\n## Claude代理测试\n"
        report += f"- 状态: {'✅ 通过' if claude_success else '❌ 失败'}\n"
        
        report += "\n## 建议\n"
        if claude_success and any("在线" in status for status in api_status.values()):
            report += "- ✅ 部署成功，可以开始使用Claude Code\n"
            report += "- 建议使用vLLM获得最佳性能\n"
            report += "- LM Studio作为稳定的备选方案\n"
        else:
            report += "- ❌ 部署存在问题，请检查服务状态\n"
            report += "- 确保所有服务都已正确启动\n"
        
        with open("test_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"\n✅ 测试报告已生成: test_report.md")

def main():
    print("GLM-4.5 + Claude Code 部署测试")
    print("=" * 50)
    
    tester = DeploymentTester()
    
    # 检查API可用性
    api_status = tester.check_api_availability()
    
    # 如果有可用的API，进行详细测试
    available_apis = [name for name, status in api_status.items() if "在线" in status]
    
    if available_apis:
        print(f"\n发现 {len(available_apis)} 个可用API，开始详细测试...")
        
        # 性能基准测试
        tester.benchmark_performance()
        
        # Claude代理测试
        tester.test_claude_proxy()
        
        # 生成报告
        tester.create_test_report()
        
        print("\n=== 测试完成 ===")
        print("查看详细报告: test_report.md")
    else:
        print("\n❌ 没有发现可用的API")
        print("请确保以下服务已启动:")
        print("  - vLLM服务器 (端口8000)")
        print("  - LM Studio (端口1234)")
        print("  - Claude代理 (端口3000)")

if __name__ == "__main__":
    main()
