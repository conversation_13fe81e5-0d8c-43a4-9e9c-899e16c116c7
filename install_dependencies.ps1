# GLM-4.5 依赖包安装脚本
# 针对 RTX 4060 8GB 优化

Write-Host "=== 安装 GLM-4.5 依赖包 ===" -ForegroundColor Green

# 激活虚拟环境
if (Test-Path ".\env\Scripts\Activate.ps1") {
    & ".\env\Scripts\Activate.ps1"
    Write-Host "✅ 虚拟环境已激活: env\" -ForegroundColor Green
} else {
    Write-Host "❌ 虚拟环境不存在，请先运行 setup_environment.ps1" -ForegroundColor Red
    Read-Host "按Enter键退出"
    exit 1
}

# 1. 安装 PyTorch (CUDA 12.1)
Write-Host "安装 PyTorch with CUDA 12.1..." -ForegroundColor Yellow
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 2. 安装 vLLM (高性能推理引擎)
Write-Host "安装 vLLM..." -ForegroundColor Yellow
pip install vllm

# 3. 安装 Transformers 和相关库
Write-Host "安装 Transformers 生态..." -ForegroundColor Yellow
pip install transformers>=4.44.0
pip install accelerate
pip install bitsandbytes
pip install auto-gptq
pip install optimum

# 4. 安装 GLM-4 相关依赖
Write-Host "安装 GLM-4 特定依赖..." -ForegroundColor Yellow
pip install zhipuai
pip install sentencepiece
pip install protobuf

# 5. 安装 API 服务器依赖
Write-Host "安装 API 服务器依赖..." -ForegroundColor Yellow
pip install fastapi
pip install uvicorn
pip install pydantic
pip install sse-starlette

# 6. 安装监控和工具
Write-Host "安装监控工具..." -ForegroundColor Yellow
pip install psutil
pip install gpustat
pip install nvidia-ml-py3

# 7. 安装 GGUF 支持 (用于 LM Studio)
Write-Host "安装 GGUF 支持..." -ForegroundColor Yellow
pip install llama-cpp-python --extra-index-url https://abetlen.github.io/llama-cpp-python/whl/cu121

# 8. 安装 claude-code-router (全局安装)
Write-Host "安装 claude-code-router..." -ForegroundColor Yellow
npm install -g @musistudio/claude-code-router

# 9. 创建 requirements.txt
Write-Host "生成 requirements.txt..." -ForegroundColor Yellow
pip freeze > requirements.txt

Write-Host "=== 依赖安装完成 ===" -ForegroundColor Green
Write-Host "验证安装:" -ForegroundColor Yellow
Write-Host "  python -c 'import torch; print(f\"PyTorch: {torch.__version__}, CUDA: {torch.cuda.is_available()}\")''" -ForegroundColor Cyan
Write-Host "  vllm --version" -ForegroundColor Cyan
Write-Host "  claude-code-router --version" -ForegroundColor Cyan
