#!/usr/bin/env python3
"""
简单的Claude API代理服务器
将Claude Code请求转发到本地GLM-4.5
"""

from flask import Flask, request, jsonify, Response
import requests
import json
import logging

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

# 本地API配置
LOCAL_APIS = {
    "vllm": "http://localhost:8000/v1",
    "lm_studio": "http://localhost:1234/v1"
}

def transform_claude_to_openai(claude_request):
    """将Claude API请求转换为OpenAI格式"""
    openai_request = {
        "model": "glm-4.5-air",
        "messages": claude_request.get("messages", []),
        "max_tokens": claude_request.get("max_tokens", 1000),
        "temperature": claude_request.get("temperature", 0.7),
        "top_p": claude_request.get("top_p", 0.9),
        "stream": claude_request.get("stream", False)
    }
    return openai_request

def transform_openai_to_claude(openai_response):
    """将OpenAI API响应转换为Claude格式"""
    if "choices" in openai_response and openai_response["choices"]:
        content = openai_response["choices"][0]["message"]["content"]
        claude_response = {
            "id": openai_response.get("id", "msg_001"),
            "type": "message",
            "role": "assistant",
            "content": [{"type": "text", "text": content}],
            "model": "claude-3-sonnet-20240229",
            "stop_reason": "end_turn",
            "stop_sequence": None,
            "usage": {
                "input_tokens": openai_response.get("usage", {}).get("prompt_tokens", 0),
                "output_tokens": openai_response.get("usage", {}).get("completion_tokens", 0)
            }
        }
        return claude_response
    return {"error": "Invalid response format"}

@app.route("/v1/messages", methods=["POST"])
def proxy_messages():
    """代理Claude messages API"""
    try:
        claude_request = request.get_json()
        openai_request = transform_claude_to_openai(claude_request)
        
        # 尝试vLLM API
        try:
            response = requests.post(
                f"{LOCAL_APIS['vllm']}/chat/completions",
                json=openai_request,
                timeout=30
            )
            if response.status_code == 200:
                openai_response = response.json()
                claude_response = transform_openai_to_claude(openai_response)
                return jsonify(claude_response)
        except Exception as e:
            app.logger.warning(f"vLLM API failed: {e}")
        
        # 回退到LM Studio API
        try:
            response = requests.post(
                f"{LOCAL_APIS['lm_studio']}/chat/completions",
                json=openai_request,
                timeout=30
            )
            if response.status_code == 200:
                openai_response = response.json()
                claude_response = transform_openai_to_claude(openai_response)
                return jsonify(claude_response)
        except Exception as e:
            app.logger.error(f"LM Studio API failed: {e}")
        
        return jsonify({"error": "All local APIs failed"}), 500
        
    except Exception as e:
        app.logger.error(f"Proxy error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route("/health", methods=["GET"])
def health_check():
    """健康检查"""
    status = {"status": "ok", "apis": {}}
    
    for name, url in LOCAL_APIS.items():
        try:
            response = requests.get(f"{url}/models", timeout=5)
            status["apis"][name] = "online" if response.status_code == 200 else "offline"
        except:
            status["apis"][name] = "offline"
    
    return jsonify(status)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=3000, debug=False)
