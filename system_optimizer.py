#!/usr/bin/env python3
"""
系统性能优化脚本
针对 i7-13700H + RTX 4060 8GB + 64GB RAM 优化
"""

import os
import sys
import json
import psutil
import subprocess
import time
import threading
from pathlib import Path
import GPUtil

class SystemOptimizer:
    def __init__(self):
        self.config_dir = Path("configs")
        self.cache_dir = Path("cache")
        self.logs_dir = Path("logs")
        
        # 创建目录
        for dir_path in [self.config_dir, self.cache_dir, self.logs_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def detect_hardware(self):
        """检测硬件配置"""
        info = {
            "cpu": {
                "model": self.get_cpu_model(),
                "cores": psutil.cpu_count(logical=False),
                "threads": psutil.cpu_count(logical=True),
                "frequency": psutil.cpu_freq().max if psutil.cpu_freq() else "Unknown"
            },
            "memory": {
                "total_gb": round(psutil.virtual_memory().total / 1024**3, 1),
                "available_gb": round(psutil.virtual_memory().available / 1024**3, 1)
            },
            "gpu": self.get_gpu_info(),
            "storage": self.get_storage_info()
        }
        
        return info
    
    def get_cpu_model(self):
        """获取CPU型号"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(
                    ['wmic', 'cpu', 'get', 'name'], 
                    capture_output=True, text=True
                )
                lines = result.stdout.strip().split('\n')
                return lines[1].strip() if len(lines) > 1 else "Unknown"
            else:  # Linux/Mac
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'model name' in line:
                            return line.split(':')[1].strip()
        except:
            return "Unknown"
        return "Unknown"
    
    def get_gpu_info(self):
        """获取GPU信息"""
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]
                return {
                    "name": gpu.name,
                    "memory_total": f"{gpu.memoryTotal}MB",
                    "memory_free": f"{gpu.memoryFree}MB",
                    "driver": gpu.driver,
                    "temperature": f"{gpu.temperature}°C"
                }
        except:
            pass
        
        # 备用方法
        try:
            import torch
            if torch.cuda.is_available():
                return {
                    "name": torch.cuda.get_device_name(0),
                    "memory_total": f"{torch.cuda.get_device_properties(0).total_memory // 1024**2}MB",
                    "cuda_version": torch.version.cuda
                }
        except:
            pass
        
        return {"name": "Unknown", "memory_total": "Unknown"}
    
    def get_storage_info(self):
        """获取存储信息"""
        try:
            usage = psutil.disk_usage('.')
            return {
                "total_gb": round(usage.total / 1024**3, 1),
                "free_gb": round(usage.free / 1024**3, 1),
                "used_gb": round(usage.used / 1024**3, 1)
            }
        except:
            return {"total_gb": "Unknown", "free_gb": "Unknown"}
    
    def optimize_windows_settings(self):
        """优化Windows系统设置"""
        optimizations = []
        
        try:
            # 设置高性能电源计划
            subprocess.run([
                'powercfg', '/setactive', '8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c'
            ], check=True, capture_output=True)
            optimizations.append("✅ 已设置高性能电源计划")
        except:
            optimizations.append("⚠️  无法设置电源计划（需要管理员权限）")
        
        try:
            # 禁用Windows Defender实时保护（临时）
            subprocess.run([
                'powershell', '-Command', 
                'Set-MpPreference -DisableRealtimeMonitoring $true'
            ], check=True, capture_output=True)
            optimizations.append("✅ 已临时禁用Windows Defender实时保护")
        except:
            optimizations.append("⚠️  无法禁用Windows Defender（需要管理员权限）")
        
        return optimizations
    
    def optimize_memory_settings(self):
        """优化内存设置"""
        # 创建内存优化配置
        memory_config = {
            "swap_usage": "minimal",  # 最小化交换文件使用
            "cache_strategy": "aggressive",  # 激进缓存策略
            "model_cache_size_gb": 16,  # 模型缓存大小
            "kv_cache_size_gb": 8,  # KV缓存大小
            "cpu_offload_gb": 12  # CPU offload大小
        }
        
        with open(self.config_dir / "memory_config.json", "w") as f:
            json.dump(memory_config, f, indent=2)
        
        return memory_config
    
    def optimize_gpu_settings(self):
        """优化GPU设置"""
        gpu_config = {
            "memory_fraction": 0.85,  # 使用85%显存
            "allow_growth": True,  # 动态显存分配
            "memory_pool_size": "6GB",  # 显存池大小
            "compute_mode": "exclusive",  # 独占计算模式
            "persistence_mode": True  # 持久化模式
        }
        
        # 设置CUDA环境变量
        cuda_env = {
            "CUDA_VISIBLE_DEVICES": "0",
            "CUDA_DEVICE_ORDER": "PCI_BUS_ID",
            "CUDA_CACHE_PATH": str(self.cache_dir / "cuda_cache"),
            "CUDA_LAUNCH_BLOCKING": "0",
            "TORCH_CUDA_ARCH_LIST": "8.6",  # RTX 4060架构
        }
        
        # 创建环境变量设置脚本
        env_script = "@echo off\n"
        for key, value in cuda_env.items():
            env_script += f"set {key}={value}\n"
        env_script += "echo CUDA环境变量已设置\n"
        
        with open("set_cuda_env.bat", "w") as f:
            f.write(env_script)
        
        with open(self.config_dir / "gpu_config.json", "w") as f:
            json.dump(gpu_config, f, indent=2)
        
        return gpu_config
    
    def create_performance_monitor(self):
        """创建性能监控脚本"""
        monitor_script = '''#!/usr/bin/env python3
"""
实时性能监控
"""

import psutil
import GPUtil
import time
import json
from datetime import datetime

def monitor_system():
    while True:
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用
            memory = psutil.virtual_memory()
            
            # GPU使用率
            gpu_info = {}
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]
                    gpu_info = {
                        "utilization": f"{gpu.load * 100:.1f}%",
                        "memory_used": f"{gpu.memoryUsed}MB",
                        "memory_total": f"{gpu.memoryTotal}MB",
                        "temperature": f"{gpu.temperature}°C"
                    }
            except:
                gpu_info = {"status": "unavailable"}
            
            # 输出监控信息
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"\\r[{timestamp}] CPU: {cpu_percent:5.1f}% | "
                  f"RAM: {memory.percent:5.1f}% ({memory.used//1024**3}GB/{memory.total//1024**3}GB) | "
                  f"GPU: {gpu_info.get('utilization', 'N/A')} "
                  f"({gpu_info.get('memory_used', 'N/A')}/{gpu_info.get('memory_total', 'N/A')})", 
                  end="", flush=True)
            
            time.sleep(2)
            
        except KeyboardInterrupt:
            print("\\n监控已停止")
            break
        except Exception as e:
            print(f"\\n监控错误: {e}")
            time.sleep(5)

if __name__ == "__main__":
    print("开始系统性能监控...")
    print("按 Ctrl+C 停止监控")
    monitor_system()
'''
        
        with open("performance_monitor.py", "w", encoding="utf-8") as f:
            f.write(monitor_script)
        
        print("✅ 性能监控脚本已创建: performance_monitor.py")
    
    def create_optimization_summary(self, hardware_info):
        """创建优化总结"""
        summary = f"""
# GLM-4.5 系统优化总结

## 硬件配置
- **CPU**: {hardware_info['cpu']['model']} ({hardware_info['cpu']['cores']}核{hardware_info['cpu']['threads']}线程)
- **内存**: {hardware_info['memory']['total_gb']}GB
- **GPU**: {hardware_info['gpu']['name']} ({hardware_info['gpu']['memory_total']})
- **存储**: {hardware_info['storage']['free_gb']}GB 可用空间

## 推荐配置

### vLLM配置 (高性能)
```bash
python vllm_server.py --action start
```
- 显存使用: 85% (~6.8GB)
- 上下文长度: 8192 tokens
- 量化: AWQ 4-bit
- CPU offload: 8GB

### LM Studio配置 (稳定)
- 模型: GLM-4.5-Air-Q4_K_M.gguf
- GPU层数: 35
- CPU线程: 8
- 上下文: 8192 tokens

## 性能优化建议

### 1. 内存优化
- 预留16GB作为模型缓存
- 使用8GB KV缓存
- 启用内存映射和锁定

### 2. GPU优化
- 使用85%显存避免OOM
- 启用持久化模式
- 设置独占计算模式

### 3. CPU优化
- 利用多线程处理
- CPU offload减轻GPU负担
- 高性能电源计划

### 4. 存储优化
- 模型文件放在NVMe SSD
- 启用CUDA缓存
- 预编译kernels

## 启动顺序
1. 运行系统优化: `python system_optimizer.py`
2. 启动LLM服务: `python vllm_server.py` 或 LM Studio
3. 启动代理: `start_claude_router.bat`
4. 设置环境: `set_claude_env.bat`
5. 启动Claude Code

## 监控和调试
- 性能监控: `python performance_monitor.py`
- 健康检查: `http://localhost:3000/health`
- vLLM状态: `http://localhost:8000/v1/models`
- LM Studio状态: `http://localhost:1234/v1/models`
"""
        
        with open("OPTIMIZATION_GUIDE.md", "w", encoding="utf-8") as f:
            f.write(summary)
        
        print("✅ 优化指南已创建: OPTIMIZATION_GUIDE.md")

def main():
    print("=== GLM-4.5 系统优化器 ===")
    
    optimizer = SystemOptimizer()
    
    # 检测硬件
    print("检测硬件配置...")
    hardware_info = optimizer.detect_hardware()
    
    print(f"\n硬件信息:")
    print(f"  CPU: {hardware_info['cpu']['model']}")
    print(f"  内存: {hardware_info['memory']['total_gb']}GB")
    print(f"  GPU: {hardware_info['gpu']['name']}")
    print(f"  存储: {hardware_info['storage']['free_gb']}GB 可用")
    
    # 系统优化
    print("\n执行系统优化...")
    if os.name == 'nt':
        optimizations = optimizer.optimize_windows_settings()
        for opt in optimizations:
            print(f"  {opt}")
    
    # 内存和GPU优化
    memory_config = optimizer.optimize_memory_settings()
    gpu_config = optimizer.optimize_gpu_settings()
    
    print("✅ 内存配置已优化")
    print("✅ GPU配置已优化")
    
    # 创建监控工具
    optimizer.create_performance_monitor()
    optimizer.create_optimization_summary(hardware_info)
    
    print("\n=== 优化完成 ===")
    print("配置文件位置:")
    print("  - configs/memory_config.json")
    print("  - configs/gpu_config.json")
    print("  - set_cuda_env.bat")
    print("  - performance_monitor.py")
    print("  - OPTIMIZATION_GUIDE.md")

if __name__ == "__main__":
    main()
