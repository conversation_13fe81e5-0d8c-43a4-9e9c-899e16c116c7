#!/usr/bin/env python3
"""
下载正确的GLM-4.5量化模型
针对RTX 4060 8GB显存优化
"""

from huggingface_hub import hf_hub_download
from pathlib import Path
import os

def download_single_file_model():
    """下载单文件GGUF模型（适合LM Studio）"""
    print("🤖 下载GLM-4.5-Air量化模型")
    print("=" * 50)
    
    # 创建目录
    lm_studio_dir = Path("models/LM_Studio")
    lm_studio_dir.mkdir(parents=True, exist_ok=True)
    
    # 选择单文件模型（不需要合并多个part文件）
    models = [
        {
            "name": "GLM-4.5-Air-Q2_K.gguf",
            "repo_id": "unsloth/GLM-4.5-Air-GGUF",
            "filename": "GLM-4.5-Air-Q2_K.gguf",
            "size": "~2.5GB",
            "description": "Q2量化，最小显存占用，适合8GB显存"
        },
        {
            "name": "GLM-4.5-Air-Q2_K_L.gguf", 
            "repo_id": "unsloth/GLM-4.5-Air-GGUF",
            "filename": "GLM-4.5-Air-Q2_K_L.gguf",
            "size": "~3GB",
            "description": "Q2_K_L量化，稍好质量"
        }
    ]
    
    for model in models:
        model_file = lm_studio_dir / model["name"]
        
        if model_file.exists():
            print(f"✅ {model['name']} 已存在")
            continue
        
        print(f"\n📥 下载 {model['name']} ({model['size']})")
        print(f"   描述: {model['description']}")
        print(f"   这是完整的GLM-4.5-Air模型，只是量化到更小精度")
        
        try:
            downloaded_file = hf_hub_download(
                repo_id=model["repo_id"],
                filename=model["filename"],
                local_dir=lm_studio_dir,
                local_dir_use_symlinks=False
            )
            
            print(f"✅ {model['name']} 下载完成")
            print(f"   位置: {downloaded_file}")
            
            # 下载一个就够了
            break
            
        except Exception as e:
            print(f"❌ {model['name']} 下载失败: {e}")
            continue
    
    print("\n🎉 GLM-4.5-Air量化模型下载完成！")
    print("\n📋 模型说明:")
    print("- 这是完整的GLM-4.5-Air模型（不是小模型）")
    print("- 通过量化技术将精度从16bit降到2-4bit")
    print("- 保持了原模型的能力，但显存占用大幅减少")
    print("- 适合在8GB显存的RTX 4060上运行")

def download_multi_part_model():
    """下载多文件模型（需要合并）"""
    print("\n🔧 下载Q4_K_M多文件模型（更高质量）")
    
    lm_studio_dir = Path("models/LM_Studio/Q4_K_M")
    lm_studio_dir.mkdir(parents=True, exist_ok=True)
    
    # Q4_K_M分为2个文件
    files = [
        "Q4_K_M/GLM-4.5-Air-Q4_K_M-00001-of-00002.gguf",
        "Q4_K_M/GLM-4.5-Air-Q4_K_M-00002-of-00002.gguf"
    ]
    
    print("📥 下载Q4_K_M量化模型（~4.5GB，分2个文件）")
    
    for filename in files:
        local_file = lm_studio_dir / Path(filename).name
        
        if local_file.exists():
            print(f"✅ {Path(filename).name} 已存在")
            continue
        
        try:
            print(f"📥 下载 {Path(filename).name}...")
            downloaded_file = hf_hub_download(
                repo_id="unsloth/GLM-4.5-Air-GGUF",
                filename=filename,
                local_dir=lm_studio_dir.parent,
                local_dir_use_symlinks=False
            )
            print(f"✅ {Path(filename).name} 下载完成")
            
        except Exception as e:
            print(f"❌ {Path(filename).name} 下载失败: {e}")
    
    print("\n💡 多文件模型使用说明:")
    print("- LM Studio可以自动识别和加载多文件模型")
    print("- 选择第一个文件，LM Studio会自动加载所有部分")

def main():
    print("GLM-4.5-Air 量化模型下载器")
    print("适用于RTX 4060 8GB显存")
    print("=" * 50)
    
    print("🔍 可选的量化级别:")
    print("- Q2_K: 2bit量化，~2.5GB，最小显存占用")
    print("- Q4_K_M: 4bit量化，~4.5GB，推荐平衡选择") 
    print("- Q5_K_M: 5bit量化，~5.5GB，更高质量")
    print()
    print("💡 说明: 这些都是完整的GLM-4.5-Air模型，不是小模型")
    print("   只是通过量化技术减少了精度以节省显存")
    
    choice = input("\n选择下载哪个版本？\n1. Q2_K (推荐8GB显存)\n2. Q4_K_M (平衡选择)\n请输入 1 或 2: ").strip()
    
    if choice == "1":
        download_single_file_model()
    elif choice == "2":
        download_multi_part_model()
    else:
        print("默认下载Q2_K版本...")
        download_single_file_model()

if __name__ == "__main__":
    main()
