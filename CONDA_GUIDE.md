# GLM-4.5 Conda环境使用指南

## 🔍 环境检测

您的项目使用的是 **Conda环境**，位于 `env/` 目录。

### 环境信息
- **类型**: Conda环境（不是标准Python venv）
- **位置**: `.\env\`
- **Python版本**: 3.10
- **激活方式**: `call activate .\env`

## 🚀 快速开始

### 1. 激活环境
```bash
# 方法1: 使用专用脚本（推荐）
activate_env.bat

# 方法2: 手动激活
call activate .\env
```

### 2. 验证环境
```bash
# 检查Python版本
python --version

# 检查环境状态
python conda_env_manager.py --status
```

### 3. 安装依赖
```bash
# 确保环境已激活，然后运行
.\install_dependencies.ps1
```

## 🛠️ 环境管理

### 使用环境管理器
```bash
# 交互式管理界面
python conda_env_manager.py

# 查看环境状态
python conda_env_manager.py --status

# 安装GLM-4.5依赖
python conda_env_manager.py --install
```

### 手动管理
```bash
# 激活环境
call activate .\env

# 安装包
pip install package_name

# 查看已安装包
pip list

# 导出环境
conda env export -p .\env -f environment.yml
```

## 🔧 常见问题

### Q1: 激活环境失败
**问题**: `call activate .\env` 报错
**解决**:
1. 确保Conda已安装并在PATH中
2. 检查环境是否存在: `dir env\python.exe`
3. 重新创建环境: `00 conda创建环境.bat`

### Q2: 包安装失败
**问题**: pip install 报错
**解决**:
1. 确保环境已激活
2. 升级pip: `python -m pip install --upgrade pip`
3. 使用conda安装: `conda install -p .\env package_name`

### Q3: 环境路径问题
**问题**: 脚本找不到环境
**解决**:
1. 确保在项目根目录运行脚本
2. 检查env目录是否存在
3. 使用绝对路径: `call activate D:\wwwroot\GLM-4.5\env`

## 📋 环境检查清单

运行以下命令验证环境设置：

```bash
# 1. 检查Conda可用性
conda --version

# 2. 检查环境存在
dir env\python.exe

# 3. 激活环境
call activate .\env

# 4. 验证Python
python --version

# 5. 检查关键包
python -c "import torch; print('PyTorch:', torch.__version__)"
python -c "import transformers; print('Transformers:', transformers.__version__)"
```

## 🎯 与GLM-4.5脚本集成

所有GLM-4.5脚本已更新以支持Conda环境：

### 自动激活脚本
- `quick_start.bat` - 图形化启动界面
- `activate_env.bat` - 专用环境激活
- `start_claude_router.bat` - Claude代理启动

### 环境感知脚本
- `deploy_glm45.py` - 自动检测Conda环境
- `config_validator.py` - 验证Conda环境配置
- `conda_env_manager.py` - 专用环境管理器

## 💡 最佳实践

### 1. 环境激活
- 始终在运行Python脚本前激活环境
- 使用 `activate_env.bat` 获得最佳体验
- 保持激活窗口打开以避免重复激活

### 2. 包管理
- 优先使用pip安装Python包
- 对于系统级依赖使用conda
- 定期导出环境配置作为备份

### 3. 故障排除
- 遇到问题时首先检查环境是否激活
- 使用 `conda_env_manager.py --status` 诊断
- 查看 `DEPLOYMENT_STATUS.md` 获取详细指导

## 🔄 环境重建

如果环境损坏，可以重新创建：

```bash
# 1. 删除现有环境
rmdir /s env

# 2. 重新创建
00 conda创建环境.bat

# 3. 激活并安装依赖
call activate .\env
.\install_dependencies.ps1
```

## 📞 技术支持

如果遇到环境相关问题：

1. 运行 `python conda_env_manager.py --status` 获取详细信息
2. 检查 `conda-meta/history` 文件查看环境历史
3. 查看项目根目录的其他 `.bat` 文件了解环境使用方式

---

**注意**: 此项目专门配置为使用Conda环境，所有脚本和文档都已相应更新。请使用Conda相关命令而不是标准的Python venv命令。
