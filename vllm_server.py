#!/usr/bin/env python3
"""
vLLM 高性能推理服务器
针对 RTX 4060 8GB 优化
"""

import os
import sys
import json
import argparse
import subprocess
import psutil
import torch
from pathlib import Path

class VLLMServer:
    def __init__(self):
        self.config_file = Path("configs/vllm_config.json")
        self.model_path = None
        self.server_process = None
        
    def detect_optimal_config(self):
        """检测最优配置"""
        # 检测GPU信息
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"GPU: {gpu_name}, 显存: {gpu_memory:.1f}GB")
        else:
            print("未检测到CUDA GPU")
            return None
        
        # 检测CPU和内存
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / 1024**3
        print(f"CPU核心: {cpu_count}, 内存: {memory_gb:.1f}GB")
        
        # 根据硬件配置生成最优参数
        if gpu_memory <= 8.5:  # RTX 4060 8GB
            config = {
                "model": "./models/GLM-4.5-Air-AWQ",
                "host": "0.0.0.0",
                "port": 8000,
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.85,
                "max_model_len": 8192,
                "quantization": "awq",
                "dtype": "half",
                "enforce_eager": True,
                "disable_custom_all_reduce": True,
                "max_num_seqs": 16,  # 减少并发序列
                "max_num_batched_tokens": 4096,
                "block_size": 16,
                "swap_space": 4,  # 4GB swap到CPU内存
                "cpu_offload_gb": 8,  # 8GB offload到CPU
            }
        else:
            # 更大显存的配置
            config = {
                "model": "./models/GLM-4.5-Air-AWQ",
                "host": "0.0.0.0", 
                "port": 8000,
                "tensor_parallel_size": 1,
                "gpu_memory_utilization": 0.9,
                "max_model_len": 16384,
                "quantization": "awq",
                "dtype": "half",
                "max_num_seqs": 32,
                "max_num_batched_tokens": 8192,
            }
        
        return config
    
    def save_config(self, config):
        """保存配置到文件"""
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置已保存到 {self.config_file}")
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, "r", encoding="utf-8") as f:
                return json.load(f)
        return None
    
    def check_model_exists(self, model_path):
        """检查模型是否存在"""
        model_dir = Path(model_path)
        if not model_dir.exists():
            print(f"❌ 模型路径不存在: {model_path}")
            return False
        
        # 检查必要文件
        required_files = ["config.json"]
        for file in required_files:
            if not (model_dir / file).exists():
                print(f"❌ 缺少必要文件: {file}")
                return False
        
        return True
    
    def start_server(self, config=None):
        """启动vLLM服务器"""
        if config is None:
            config = self.load_config()
            if config is None:
                print("❌ 未找到配置文件，正在生成...")
                config = self.detect_optimal_config()
                self.save_config(config)
        
        model_path = config["model"]
        if not self.check_model_exists(model_path):
            print("❌ 模型检查失败，请先下载模型")
            return False
        
        # 构建vLLM启动命令
        cmd = [
            "python", "-m", "vllm.entrypoints.openai.api_server",
            "--model", model_path,
            "--host", config.get("host", "0.0.0.0"),
            "--port", str(config.get("port", 8000)),
            "--tensor-parallel-size", str(config.get("tensor_parallel_size", 1)),
            "--gpu-memory-utilization", str(config.get("gpu_memory_utilization", 0.85)),
            "--max-model-len", str(config.get("max_model_len", 8192)),
        ]
        
        # 添加可选参数
        if config.get("quantization"):
            cmd.extend(["--quantization", config["quantization"]])
        
        if config.get("dtype"):
            cmd.extend(["--dtype", config["dtype"]])
        
        if config.get("enforce_eager"):
            cmd.append("--enforce-eager")
        
        if config.get("disable_custom_all_reduce"):
            cmd.append("--disable-custom-all-reduce")
        
        if config.get("max_num_seqs"):
            cmd.extend(["--max-num-seqs", str(config["max_num_seqs"])])
        
        if config.get("max_num_batched_tokens"):
            cmd.extend(["--max-num-batched-tokens", str(config["max_num_batched_tokens"])])
        
        print("启动vLLM服务器...")
        print(f"命令: {' '.join(cmd)}")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env["CUDA_VISIBLE_DEVICES"] = "0"
            env["VLLM_USE_MODELSCOPE"] = "False"
            
            self.server_process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            print("✅ vLLM服务器已启动")
            print(f"API地址: http://localhost:{config.get('port', 8000)}")
            print("正在等待服务器就绪...")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def monitor_server(self):
        """监控服务器输出"""
        if not self.server_process:
            print("❌ 服务器未启动")
            return
        
        try:
            for line in iter(self.server_process.stdout.readline, ''):
                print(line.strip())
                
                # 检查服务器是否就绪
                if "Application startup complete" in line:
                    print("🚀 vLLM服务器就绪!")
                
                # 检查错误
                if "ERROR" in line or "CUDA out of memory" in line:
                    print("❌ 检测到错误，请检查配置")
                
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            self.stop_server()
    
    def stop_server(self):
        """停止服务器"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("✅ vLLM服务器已停止")
    
    def test_api(self, port=8000):
        """测试API连接"""
        import requests
        import time
        
        api_url = f"http://localhost:{port}/v1"
        
        # 等待服务器启动
        for i in range(30):
            try:
                response = requests.get(f"{api_url}/models", timeout=5)
                if response.status_code == 200:
                    print("✅ API服务器连接成功")
                    break
            except:
                time.sleep(2)
                print(f"等待API服务器启动... ({i+1}/30)")
        else:
            print("❌ API服务器连接超时")
            return False
        
        # 测试聊天API
        try:
            test_payload = {
                "model": "glm-4.5-air",
                "messages": [
                    {"role": "user", "content": "你好，请简单介绍一下你自己。"}
                ],
                "max_tokens": 100,
                "temperature": 0.7
            }
            
            response = requests.post(
                f"{api_url}/chat/completions",
                json=test_payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"✅ 聊天API测试成功")
                print(f"模型响应: {content[:100]}...")
                return True
            else:
                print(f"❌ 聊天API测试失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API测试异常: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description="vLLM GLM-4.5 服务器")
    parser.add_argument("--action", choices=["start", "test", "config"], 
                       default="start", help="执行的操作")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--model", type=str, help="模型路径")
    
    args = parser.parse_args()
    
    server = VLLMServer()
    
    if args.action == "config":
        print("=== 生成vLLM配置 ===")
        config = server.detect_optimal_config()
        if config:
            if args.model:
                config["model"] = args.model
            if args.port != 8000:
                config["port"] = args.port
            server.save_config(config)
    
    elif args.action == "test":
        print("=== 测试vLLM API ===")
        server.test_api(args.port)
    
    elif args.action == "start":
        print("=== 启动vLLM服务器 ===")
        if server.start_server():
            server.monitor_server()

if __name__ == "__main__":
    main()
