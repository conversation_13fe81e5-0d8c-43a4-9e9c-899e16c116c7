#!/usr/bin/env python3
"""
基础设置测试脚本
验证当前环境和基础功能
"""

import sys
import os
from pathlib import Path

def test_python_environment():
    """测试Python环境"""
    print("🐍 Python环境测试")
    print(f"  Python版本: {sys.version}")
    print(f"  Python路径: {sys.executable}")
    
    # 检查是否在Conda环境中
    if 'CONDA_PREFIX' in os.environ:
        print(f"  Conda环境: {os.environ['CONDA_PREFIX']}")
    elif hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix:
        print(f"  虚拟环境: {sys.prefix}")
    else:
        print("  系统Python环境")

def test_basic_imports():
    """测试基础包导入"""
    print("\n📦 基础包测试")
    
    packages = [
        ("requests", "HTTP请求库"),
        ("flask", "Web框架"),
        ("fastapi", "现代Web框架"),
        ("psutil", "系统监控"),
        ("json", "JSON处理"),
        ("pathlib", "路径处理")
    ]
    
    for package, description in packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ❌ {package} - {description} (未安装)")

def test_advanced_imports():
    """测试高级包导入"""
    print("\n🤖 AI相关包测试")
    
    packages = [
        ("torch", "PyTorch深度学习框架"),
        ("transformers", "Hugging Face Transformers"),
        ("huggingface_hub", "Hugging Face Hub"),
        ("vllm", "vLLM推理引擎"),
        ("sentencepiece", "文本分词器"),
        ("protobuf", "协议缓冲区")
    ]
    
    for package, description in packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ❌ {package} - {description} (未安装)")

def test_file_structure():
    """测试文件结构"""
    print("\n📁 文件结构测试")
    
    required_files = [
        "deploy_glm45.py",
        "vllm_server.py",
        "claude_proxy.py",
        "download_models.py",
        "config_validator.py",
        "conda_env_manager.py"
    ]
    
    required_dirs = [
        "models",
        "configs", 
        "cache",
        "logs",
        "env"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {dir_path}/")
        else:
            print(f"  ❌ {dir_path}/ (缺失)")

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 网络连接测试")
    
    try:
        import requests
        
        # 测试基本连接
        response = requests.get("https://www.baidu.com", timeout=10)
        if response.status_code == 200:
            print("  ✅ 基本网络连接正常")
        else:
            print("  ⚠️  网络连接异常")
            
        # 测试Hugging Face
        response = requests.get("https://huggingface.co", timeout=10)
        if response.status_code == 200:
            print("  ✅ Hugging Face可访问")
        else:
            print("  ⚠️  Hugging Face不可访问")
            
    except Exception as e:
        print(f"  ❌ 网络测试失败: {e}")

def test_gpu_availability():
    """测试GPU可用性"""
    print("\n🎮 GPU测试")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            print(f"  ✅ CUDA可用，检测到 {gpu_count} 个GPU")
            
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"    GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            print("  ❌ CUDA不可用")
    except ImportError:
        print("  ❌ PyTorch未安装，无法检测GPU")

def test_simple_flask_server():
    """测试简单的Flask服务器"""
    print("\n🌐 Flask服务器测试")
    
    try:
        from flask import Flask, jsonify
        import threading
        import time
        import requests
        
        app = Flask(__name__)
        
        @app.route('/test')
        def test_endpoint():
            return jsonify({"status": "ok", "message": "Flask服务器正常工作"})
        
        # 在后台启动服务器
        def run_server():
            app.run(host='localhost', port=5555, debug=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        # 测试连接
        response = requests.get("http://localhost:5555/test", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Flask服务器测试成功: {result['message']}")
        else:
            print("  ❌ Flask服务器响应异常")
            
    except Exception as e:
        print(f"  ❌ Flask服务器测试失败: {e}")

def generate_summary():
    """生成测试总结"""
    print("\n" + "="*60)
    print("📋 测试总结")
    print("="*60)
    
    print("✅ 已完成的设置:")
    print("  - Python 3.10.18 环境")
    print("  - Conda环境配置")
    print("  - 基础Web框架 (Flask, FastAPI)")
    print("  - HTTP请求库 (requests)")
    print("  - 系统监控库 (psutil)")
    print("  - 项目目录结构")
    
    print("\n⚠️  待完成的设置:")
    print("  - PyTorch和CUDA支持")
    print("  - Transformers和Hugging Face Hub")
    print("  - vLLM推理引擎")
    print("  - GLM-4.5模型下载")
    
    print("\n🚀 下一步建议:")
    print("  1. 安装PyTorch: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
    print("  2. 安装AI包: pip install transformers huggingface_hub vllm")
    print("  3. 下载模型: python download_models.py")
    print("  4. 启动服务: python claude_proxy.py")

def main():
    print("🔍 GLM-4.5 基础设置测试")
    print("="*60)
    
    test_python_environment()
    test_basic_imports()
    test_advanced_imports()
    test_file_structure()
    test_network_connectivity()
    test_gpu_availability()
    test_simple_flask_server()
    generate_summary()

if __name__ == "__main__":
    main()
