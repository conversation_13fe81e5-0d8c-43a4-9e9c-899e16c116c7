@echo off
chcp 65001 >nul
title GLM-4.5 + Claude Code 快速启动

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                GLM-4.5 + Claude Code 快速启动                ║
echo ║              适用于 RTX 4060 8GB + 64GB RAM                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查是否在正确的目录
if not exist "deploy_glm45.py" (
    echo ❌ 错误: 请在GLM-4.5项目根目录运行此脚本
    pause
    exit /b 1
)

REM 激活Conda环境
if exist "env\python.exe" (
    echo 🔧 激活Conda环境...
    call activate .\env
    if errorlevel 1 (
        echo ⚠️  Conda环境激活失败，使用系统Python
    ) else (
        echo ✅ Conda环境已激活
    )
) else (
    echo ⚠️  Conda环境不存在，使用系统Python
    echo    请运行: 00 conda创建环境.bat
)

:menu
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    GLM-4.5 控制面板                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📊 系统状态检查
echo   1. 检查服务状态
echo   2. 运行系统监控
echo.
echo 🚀 服务管理
echo   3. 启动vLLM服务器 (推荐高性能)
echo   4. 启动LM Studio (推荐稳定)
echo   5. 启动Claude代理
echo   6. 停止所有服务
echo.
echo 🔄 模型切换
echo   7. 切换到vLLM
echo   8. 切换到LM Studio
echo   9. 模型切换工具
echo.
echo 🧪 测试工具
echo   A. 快速测试部署
echo   B. 完整性能基准测试
echo   C. 服务监控器
echo.
echo 🛠️  维护工具
echo   D. 重新下载模型
echo   E. 系统优化
echo   F. 查看日志
echo.
echo   0. 退出
echo.
set /p choice="请选择操作 (1-F): "

if "%choice%"=="1" goto check_status
if "%choice%"=="2" goto monitor
if "%choice%"=="3" goto start_vllm
if "%choice%"=="4" goto start_lm_studio
if "%choice%"=="5" goto start_proxy
if "%choice%"=="6" goto stop_all
if "%choice%"=="7" goto switch_vllm
if "%choice%"=="8" goto switch_lm_studio
if "%choice%"=="9" goto model_switcher
if /i "%choice%"=="A" goto quick_test
if /i "%choice%"=="B" goto benchmark
if /i "%choice%"=="C" goto service_monitor
if /i "%choice%"=="D" goto download_models
if /i "%choice%"=="E" goto optimize
if /i "%choice%"=="F" goto view_logs
if "%choice%"=="0" goto exit

echo 无效选择，请重试
pause
goto menu

:check_status
echo.
echo 🔍 检查服务状态...
python test_deployment.py
pause
goto menu

:monitor
echo.
echo 📊 启动性能监控...
echo 按 Ctrl+C 停止监控
python performance_monitor.py
pause
goto menu

:start_vllm
echo.
echo 🚀 启动vLLM服务器...
echo 这可能需要几分钟时间，请耐心等待...
start "vLLM Server" cmd /k "python vllm_server.py"
echo ✅ vLLM服务器已在新窗口中启动
pause
goto menu

:start_lm_studio
echo.
echo 🚀 启动LM Studio...
call start_lm_studio.bat
pause
goto menu

:start_proxy
echo.
echo 🚀 启动Claude代理...
start "Claude Proxy" cmd /k "python claude_proxy.py"
echo ✅ Claude代理已在新窗口中启动
pause
goto menu

:stop_all
echo.
echo 🛑 停止所有服务...
taskkill /f /im python.exe /fi "WINDOWTITLE eq vLLM Server*" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq Claude Proxy*" 2>nul
echo ✅ 服务已停止
pause
goto menu

:switch_vllm
echo.
echo 🔄 切换到vLLM...
python model_switcher.py --engine vllm
pause
goto menu

:switch_lm_studio
echo.
echo 🔄 切换到LM Studio...
python model_switcher.py --engine lm_studio
pause
goto menu

:model_switcher
echo.
echo 🔄 启动模型切换工具...
python model_switcher.py
pause
goto menu

:quick_test
echo.
echo 🧪 运行快速测试...
python test_deployment.py
pause
goto menu

:benchmark
echo.
echo 🧪 运行完整性能基准测试...
echo 这可能需要较长时间，请耐心等待...
python benchmark_suite.py
pause
goto menu

:service_monitor
echo.
echo 🔧 启动服务监控器...
python service_monitor.py
pause
goto menu

:download_models
echo.
echo 📥 重新下载模型...
python download_models.py
pause
goto menu

:optimize
echo.
echo ⚡ 执行系统优化...
python system_optimizer.py
pause
goto menu

:view_logs
echo.
echo 📋 查看日志文件...
if exist "logs" (
    explorer logs
) else (
    echo 日志目录不存在
)
pause
goto menu

:exit
echo.
echo 👋 感谢使用GLM-4.5 + Claude Code部署方案！
echo.
pause
