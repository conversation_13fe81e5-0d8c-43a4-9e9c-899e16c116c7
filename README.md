# GLM-4.5 + <PERSON> Code 本地部署方案

针对 **RTX 4060 8GB + 64GB RAM + i7-13700H** 硬件配置优化的GLM-4.5本地部署方案，集成Claude Code功能。

## 🎯 项目目标

- 在8GB显存限制下高效运行GLM-4.5
- 通过claude-code-router将Claude Code请求路由到本地模型
- 充分利用CPU+GPU+内存资源
- 提供稳定可靠的本地AI编程助手

## 🔧 硬件要求

- **GPU**: RTX 4060 8GB (或同等级别)
- **CPU**: Intel i7-13700H (或同等性能)
- **内存**: 64GB RAM
- **存储**: NVMe SSD (至少50GB可用空间)
- **系统**: Windows 11

## 📦 软件依赖

- Python 3.11+
- CUDA Toolkit 12.1+
- Node.js 18+
- Git
- LM Studio (可选)

## 🚀 快速开始

### 1. 一键部署

```bash
# 克隆或下载项目文件到 D:\wwwroot\GLM-4.5
cd D:\wwwroot\GLM-4.5

# 运行一键部署脚本
python deploy_glm45.py
```

### 2. 手动部署

#### 步骤1: 环境准备
```bash
# 以管理员身份运行
.\setup_environment.ps1

# 安装依赖
.\install_dependencies.ps1
```

#### 步骤2: 下载模型
```bash
# 激活虚拟环境
.\venv\Scripts\activate

# 下载GLM-4.5模型
python download_models.py
```

#### 步骤3: 系统优化
```bash
# 执行系统优化
python system_optimizer.py
```

#### 步骤4: 启动服务

**方案A: 使用vLLM (推荐高性能)**
```bash
# 启动vLLM服务器
python vllm_server.py
```

**方案B: 使用LM Studio (推荐稳定性)**
```bash
# 启动LM Studio
start_lm_studio.bat

# 在LM Studio中:
# 1. 加载 models/LM_Studio/GLM-4.5-Air-Q4_K_M.gguf
# 2. 设置GPU Layers: 35
# 3. 启动本地服务器 (端口1234)
```

#### 步骤5: 启动Claude代理
```bash
# 启动Claude Code Router
start_claude_router.bat
```

#### 步骤6: 配置Claude Code
```bash
# 设置环境变量
set_claude_env.bat

# 启动Claude Code并开始使用
```

## 📊 性能配置

### vLLM配置 (高性能)
- **显存使用**: 85% (~6.8GB)
- **上下文长度**: 8192 tokens
- **量化**: AWQ 4-bit
- **CPU offload**: 8GB
- **推理速度**: ~15-25 tokens/s

### LM Studio配置 (稳定)
- **模型**: GLM-4.5-Air-Q4_K_M.gguf
- **GPU层数**: 35
- **CPU线程**: 8
- **上下文**: 8192 tokens
- **推理速度**: ~10-20 tokens/s

## 🔍 监控和测试

### 性能监控
```bash
# 实时性能监控
python performance_monitor.py
```

### 健康检查
- vLLM API: http://localhost:8000/v1/models
- LM Studio API: http://localhost:1234/v1/models
- Claude代理: http://localhost:3000/health

### 部署测试
```bash
# 完整测试
python test_deployment.py

# 快速测试
python deploy_glm45.py --quick-test
```

## 📁 项目结构

```
GLM-4.5/
├── models/                     # 模型文件
│   ├── GLM-4.5-Air-AWQ/       # vLLM使用的AWQ量化模型
│   └── LM_Studio/             # LM Studio使用的GGUF模型
├── configs/                   # 配置文件
│   ├── vllm_config.json
│   ├── lm_studio_config.json
│   └── claude_router/
├── cache/                     # 缓存目录
├── logs/                      # 日志文件
├── scripts/                   # 辅助脚本
├── venv/                      # Python虚拟环境
├── deploy_glm45.py           # 一键部署脚本
├── vllm_server.py            # vLLM服务器
├── claude_proxy.py           # Claude代理服务器
├── test_deployment.py        # 测试脚本
└── README.md                 # 本文档
```

## ⚡ 性能优化建议

### 显存优化
- 使用AWQ/GGUF量化减少显存占用
- 启用CPU offload将部分计算转移到CPU
- 调整max_model_len适应显存限制

### CPU优化
- 利用i7-13700H的多线程能力
- 设置高性能电源计划
- 优化线程数配置

### 内存优化
- 64GB大内存支持大型模型缓存
- 启用内存映射和锁定
- 预留足够swap空间

## 🛠️ 故障排除

### 常见问题

**1. CUDA out of memory**
```bash
# 解决方案:
# - 减少gpu_memory_utilization到0.75
# - 降低max_model_len到4096
# - 使用更激进的量化 (Q3_K_M)
```

**2. 推理速度慢**
```bash
# 解决方案:
# - 增加GPU layers
# - 减少并发请求数
# - 检查CPU温度和频率
```

**3. Claude Code连接失败**
```bash
# 检查步骤:
# 1. 确认代理服务器运行: http://localhost:3000/health
# 2. 检查环境变量设置
# 3. 验证本地LLM API可用性
```

### 日志查看
```bash
# vLLM日志
tail -f logs/vllm.log

# Claude Router日志
tail -f logs/claude_router.log

# 系统监控
python performance_monitor.py
```

## 📚 相关文档

- [OPTIMIZATION_GUIDE.md](OPTIMIZATION_GUIDE.md) - 详细优化指南
- [LM_Studio_Guide.md](LM_Studio_Guide.md) - LM Studio使用指南
- [test_report.md](test_report.md) - 最新测试报告

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个部署方案。

## 📄 许可证

本项目遵循MIT许可证。

## 🙏 致谢

- [GLM-4.5](https://github.com/THUDM/GLM-4) - 智谱AI开源模型
- [vLLM](https://github.com/vllm-project/vllm) - 高性能推理引擎
- [claude-code-router](https://github.com/musistudio/claude-code-router) - Claude代理路由器
- [LM Studio](https://lmstudio.ai/) - 本地模型管理工具
