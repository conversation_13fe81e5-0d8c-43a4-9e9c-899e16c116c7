"""Servers as another entry point for `InquirerPy`.

See Also:
    :ref:`index:Alternate Syntax`.

`inquirer` directly interact with individual prompt classes. It’s more flexible, easier to customise and also provides IDE type hintings/completions.
"""
from InquirerPy.prompts import <PERSON>boxPrompt as checkbox
from InquirerPy.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as confirm
from InquirerPy.prompts import ExpandPrompt as expand
from InquirerPy.prompts import FilePathPrompt as filepath
from InquirerPy.prompts import Fuzzy<PERSON>rompt as fuzzy
from InquirerPy.prompts import InputPrompt as text
from InquirerPy.prompts import <PERSON><PERSON>rompt as select
from InquirerPy.prompts import <PERSON><PERSON>rompt as number
from InquirerPy.prompts import <PERSON><PERSON><PERSON>rompt as rawlist
from InquirerPy.prompts import SecretPrompt as secret
