#!/usr/bin/env python3
"""
GLM-4.5 配置验证工具
检查部署环境和配置的完整性
"""

import os
import sys
import json
import subprocess
import requests
from pathlib import Path
import importlib.util

class ConfigValidator:
    def __init__(self):
        self.project_root = Path.cwd()
        self.required_files = [
            "deploy_glm45.py",
            "vllm_server.py", 
            "claude_proxy.py",
            "download_models.py",
            "setup_lm_studio.py",
            "setup_claude_router.py",
            "system_optimizer.py",
            "test_deployment.py"
        ]
        
        self.required_dirs = [
            "models",
            "configs", 
            "cache",
            "logs"
        ]
        
        self.required_packages = [
            "torch",
            "transformers", 
            "vllm",
            "fastapi",
            "requests",
            "flask",
            "psutil",
            "huggingface_hub"
        ]
        
        self.config_files = [
            "configs/vllm_config.json",
            "configs/lm_studio_config.json"
        ]
        
        self.validation_results = {}
    
    def validate_file_structure(self):
        """验证文件结构"""
        print("🔍 验证文件结构...")
        results = {"missing_files": [], "missing_dirs": [], "status": "pass"}
        
        # 检查必需文件
        for file_path in self.required_files:
            if not (self.project_root / file_path).exists():
                results["missing_files"].append(file_path)
                results["status"] = "fail"
            else:
                print(f"  ✅ {file_path}")
        
        # 检查必需目录
        for dir_path in self.required_dirs:
            if not (self.project_root / dir_path).exists():
                results["missing_dirs"].append(dir_path)
                results["status"] = "fail"
            else:
                print(f"  ✅ {dir_path}/")
        
        if results["missing_files"]:
            print(f"  ❌ 缺少文件: {', '.join(results['missing_files'])}")
        
        if results["missing_dirs"]:
            print(f"  ❌ 缺少目录: {', '.join(results['missing_dirs'])}")
        
        return results
    
    def validate_python_environment(self):
        """验证Python环境"""
        print("\n🐍 验证Python环境...")
        results = {"python_version": None, "venv_active": False, "missing_packages": [], "status": "pass"}
        
        # 检查Python版本
        python_version = sys.version_info
        results["python_version"] = f"{python_version.major}.{python_version.minor}.{python_version.micro}"
        
        if python_version.major != 3 or python_version.minor < 8:
            print(f"  ❌ Python版本过低: {results['python_version']} (需要3.8+)")
            results["status"] = "fail"
        else:
            print(f"  ✅ Python版本: {results['python_version']}")
        
        # 检查Conda环境
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            results["venv_active"] = True
            print("  ✅ Conda环境已激活")
        else:
            # 检查env目录是否存在
            if (self.project_root / "env" / "python.exe").exists():
                print("  ⚠️  Conda环境未激活（但env目录存在）")
                print("     请运行: call activate .\\env")
            else:
                print("  ⚠️  Conda环境不存在，请先运行 00 conda创建环境.bat")
        
        # 检查必需包
        for package in self.required_packages:
            try:
                spec = importlib.util.find_spec(package)
                if spec is None:
                    results["missing_packages"].append(package)
                    results["status"] = "fail"
                else:
                    print(f"  ✅ {package}")
            except ImportError:
                results["missing_packages"].append(package)
                results["status"] = "fail"
        
        if results["missing_packages"]:
            print(f"  ❌ 缺少包: {', '.join(results['missing_packages'])}")
        
        return results
    
    def validate_system_requirements(self):
        """验证系统要求"""
        print("\n💻 验证系统要求...")
        results = {"cuda_available": False, "gpu_memory": 0, "system_memory": 0, "status": "pass"}
        
        # 检查CUDA
        try:
            import torch
            if torch.cuda.is_available():
                results["cuda_available"] = True
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                results["gpu_memory"] = round(gpu_memory, 1)
                gpu_name = torch.cuda.get_device_name(0)
                print(f"  ✅ CUDA可用: {gpu_name} ({results['gpu_memory']}GB)")
                
                if results["gpu_memory"] < 7:
                    print("  ⚠️  显存可能不足，建议使用更激进的量化")
            else:
                print("  ❌ CUDA不可用")
                results["status"] = "warning"
        except ImportError:
            print("  ❌ PyTorch未安装")
            results["status"] = "fail"
        
        # 检查系统内存
        try:
            import psutil
            memory = psutil.virtual_memory()
            results["system_memory"] = round(memory.total / 1024**3, 1)
            print(f"  ✅ 系统内存: {results['system_memory']}GB")
            
            if results["system_memory"] < 16:
                print("  ⚠️  系统内存较少，可能影响性能")
                results["status"] = "warning"
        except ImportError:
            print("  ❌ psutil未安装")
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                node_version = result.stdout.strip()
                print(f"  ✅ Node.js: {node_version}")
            else:
                print("  ❌ Node.js未安装")
                results["status"] = "fail"
        except FileNotFoundError:
            print("  ❌ Node.js未安装")
            results["status"] = "fail"
        
        return results
    
    def validate_model_files(self):
        """验证模型文件"""
        print("\n🤖 验证模型文件...")
        results = {"vllm_model": False, "lm_studio_model": False, "status": "warning"}
        
        # 检查vLLM模型
        vllm_model_paths = [
            "models/GLM-4.5-Air-AWQ",
            "models/GLM-4.5-Air-Original"
        ]
        
        for model_path in vllm_model_paths:
            if (self.project_root / model_path).exists():
                config_file = self.project_root / model_path / "config.json"
                if config_file.exists():
                    results["vllm_model"] = True
                    print(f"  ✅ vLLM模型: {model_path}")
                    break
        
        if not results["vllm_model"]:
            print("  ⚠️  vLLM模型未找到")
        
        # 检查LM Studio模型
        lm_studio_dir = self.project_root / "models" / "LM_Studio"
        if lm_studio_dir.exists():
            gguf_files = list(lm_studio_dir.glob("*.gguf"))
            if gguf_files:
                results["lm_studio_model"] = True
                print(f"  ✅ LM Studio模型: {len(gguf_files)}个GGUF文件")
            else:
                print("  ⚠️  LM Studio模型未找到")
        else:
            print("  ⚠️  LM Studio模型目录不存在")
        
        if results["vllm_model"] or results["lm_studio_model"]:
            results["status"] = "pass"
        
        return results
    
    def validate_config_files(self):
        """验证配置文件"""
        print("\n⚙️  验证配置文件...")
        results = {"valid_configs": [], "invalid_configs": [], "status": "pass"}
        
        for config_path in self.config_files:
            config_file = self.project_root / config_path
            
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    # 基本验证
                    if isinstance(config_data, dict) and config_data:
                        results["valid_configs"].append(config_path)
                        print(f"  ✅ {config_path}")
                    else:
                        results["invalid_configs"].append(config_path)
                        print(f"  ❌ {config_path} (格式错误)")
                        results["status"] = "fail"
                        
                except json.JSONDecodeError:
                    results["invalid_configs"].append(config_path)
                    print(f"  ❌ {config_path} (JSON格式错误)")
                    results["status"] = "fail"
            else:
                print(f"  ⚠️  {config_path} (不存在)")
        
        return results
    
    def validate_network_connectivity(self):
        """验证网络连接"""
        print("\n🌐 验证网络连接...")
        results = {"huggingface": False, "github": False, "status": "warning"}
        
        # 测试Hugging Face连接
        try:
            response = requests.get("https://huggingface.co", timeout=10)
            if response.status_code == 200:
                results["huggingface"] = True
                print("  ✅ Hugging Face可访问")
            else:
                print("  ❌ Hugging Face不可访问")
        except:
            print("  ❌ Hugging Face连接失败")
        
        # 测试GitHub连接
        try:
            response = requests.get("https://github.com", timeout=10)
            if response.status_code == 200:
                results["github"] = True
                print("  ✅ GitHub可访问")
            else:
                print("  ❌ GitHub不可访问")
        except:
            print("  ❌ GitHub连接失败")
        
        if results["huggingface"] and results["github"]:
            results["status"] = "pass"
        
        return results
    
    def run_full_validation(self):
        """运行完整验证"""
        print("🔍 GLM-4.5 配置验证")
        print("=" * 50)
        
        # 运行所有验证
        self.validation_results = {
            "file_structure": self.validate_file_structure(),
            "python_environment": self.validate_python_environment(),
            "system_requirements": self.validate_system_requirements(),
            "model_files": self.validate_model_files(),
            "config_files": self.validate_config_files(),
            "network_connectivity": self.validate_network_connectivity()
        }
        
        # 生成总结
        self.generate_summary()
        
        return self.validation_results
    
    def generate_summary(self):
        """生成验证总结"""
        print("\n" + "=" * 50)
        print("📋 验证总结")
        print("=" * 50)
        
        total_checks = len(self.validation_results)
        passed_checks = sum(1 for result in self.validation_results.values() if result["status"] == "pass")
        warning_checks = sum(1 for result in self.validation_results.values() if result["status"] == "warning")
        failed_checks = sum(1 for result in self.validation_results.values() if result["status"] == "fail")
        
        print(f"总检查项: {total_checks}")
        print(f"✅ 通过: {passed_checks}")
        print(f"⚠️  警告: {warning_checks}")
        print(f"❌ 失败: {failed_checks}")
        
        # 详细建议
        print("\n📝 建议:")
        
        if failed_checks > 0:
            print("❌ 存在严重问题，需要修复:")
            
            if self.validation_results["file_structure"]["status"] == "fail":
                print("  - 运行 python deploy_glm45.py 重新部署")
            
            if self.validation_results["python_environment"]["status"] == "fail":
                print("  - 运行 install_dependencies.ps1 安装依赖")
            
            if self.validation_results["system_requirements"]["status"] == "fail":
                print("  - 安装CUDA Toolkit和Node.js")
        
        elif warning_checks > 0:
            print("⚠️  存在警告，建议处理:")
            
            if self.validation_results["model_files"]["status"] == "warning":
                print("  - 运行 python download_models.py 下载模型")
            
            if self.validation_results["network_connectivity"]["status"] == "warning":
                print("  - 检查网络连接或配置代理")
        
        else:
            print("✅ 所有检查通过，可以开始使用!")
            print("  - 运行 quick_start.bat 启动服务")
            print("  - 或运行 python test_deployment.py 测试部署")
    
    def fix_common_issues(self):
        """修复常见问题"""
        print("\n🔧 自动修复常见问题...")
        
        # 创建缺失目录
        for dir_path in self.required_dirs:
            dir_full_path = self.project_root / dir_path
            if not dir_full_path.exists():
                dir_full_path.mkdir(exist_ok=True)
                print(f"  ✅ 创建目录: {dir_path}")
        
        # 生成默认配置文件
        if not (self.project_root / "configs" / "vllm_config.json").exists():
            default_vllm_config = {
                "model": "./models/GLM-4.5-Air-AWQ",
                "host": "0.0.0.0",
                "port": 8000,
                "gpu_memory_utilization": 0.85,
                "max_model_len": 8192,
                "quantization": "awq"
            }
            
            with open(self.project_root / "configs" / "vllm_config.json", "w") as f:
                json.dump(default_vllm_config, f, indent=2)
            print("  ✅ 创建默认vLLM配置")
        
        print("  ✅ 自动修复完成")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="GLM-4.5 配置验证工具")
    parser.add_argument("--fix", action="store_true", help="自动修复常见问题")
    parser.add_argument("--quick", action="store_true", help="快速检查")
    
    args = parser.parse_args()
    
    validator = ConfigValidator()
    
    if args.fix:
        validator.fix_common_issues()
    
    if args.quick:
        # 只检查关键项
        validator.validate_file_structure()
        validator.validate_python_environment()
    else:
        # 完整验证
        validator.run_full_validation()

if __name__ == "__main__":
    main()
