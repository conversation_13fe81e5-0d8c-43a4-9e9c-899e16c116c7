Metadata-Version: 2.1
Name: backports.tarfile
Version: 1.2.0
Summary: Backport of CPython tarfile module
Author-email: "<PERSON>" <<EMAIL>>
Project-URL: Homepage, https://github.com/jaraco/backports.tarfile
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: docs
Requires-Dist: sphinx >=3.5 ; extra == 'docs'
Requires-Dist: jaraco.packaging >=9.3 ; extra == 'docs'
Requires-Dist: rst.linker >=1.9 ; extra == 'docs'
Requires-Dist: furo ; extra == 'docs'
Requires-Dist: sphinx-lint ; extra == 'docs'
Provides-Extra: testing
Requires-Dist: pytest !=8.1.*,>=6 ; extra == 'testing'
Requires-Dist: pytest-checkdocs >=2.4 ; extra == 'testing'
Requires-Dist: pytest-cov ; extra == 'testing'
Requires-Dist: pytest-enabler >=2.2 ; extra == 'testing'
Requires-Dist: jaraco.test ; extra == 'testing'
Requires-Dist: pytest !=8.0.* ; extra == 'testing'

.. image:: https://img.shields.io/pypi/v/backports.tarfile.svg
   :target: https://pypi.org/project/backports.tarfile

.. image:: https://img.shields.io/pypi/pyversions/backports.tarfile.svg

.. image:: https://github.com/jaraco/backports.tarfile/actions/workflows/main.yml/badge.svg
   :target: https://github.com/jaraco/backports.tarfile/actions?query=workflow%3A%22tests%22
   :alt: tests

.. image:: https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json
    :target: https://github.com/astral-sh/ruff
    :alt: Ruff

.. .. image:: https://readthedocs.org/projects/backportstarfile/badge/?version=latest
..    :target: https://backportstarfile.readthedocs.io/en/latest/?badge=latest

.. image:: https://img.shields.io/badge/skeleton-2024-informational
   :target: https://blog.jaraco.com/skeleton
